# 百度智能云CLI工具 研发须知

## 本地研发

clone代码后：

```shell
npm install

# 使用link作为当前npm版本下的全局库
npm link
```

在产品如console-demo目录下：
```shell
npm i
# 将该产品node_modules中的@baidu/bce-cli的资源目录软链到刚才link的地址
npm link @baidu/bce-cli

# 直接使用cli的一些指令即可
npm run dev
```

## 功能研发
本地开发后除提交代码外，将改动内容在README.md中进行描述，提交代码评审，评审通过后，执行版本发布，写CHANGELOG。

## 发布版本
### 发布正式版本
```shell
npm publish
```

### 发布内测版本
```shell
npm publish

npm publish --tag=dev
```

