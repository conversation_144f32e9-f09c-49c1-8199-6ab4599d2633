# 百度智能云CLI工具

> @baidu/bce-cli是智能云提供的统一Console项目脚手架工具，提供项目初始化、本地调试、各工具接入及多项目交付的能力。CLI工具严格遵守[百度智能云前端规范](http://sandbox.bce.console.baidu-int.com/bce-docs/)，不符合规范的项目不予支持

## 安装
全局安装@baidu/bce-cli，镜像源使用百度镜像：

```
npm install @baidu/bce-cli -g --registry=http://registry.npm.baidu-int.com
```

## 使用
@baidu/bce-cli的所有自定义配置均可在bce-config.js进行定义，内容包括：

* name：工具模块名称，例如：@baidu/bce-bcc-sdk
* service：在P3M平台注册的service type，默认name全大写
* pathname：表明访问的path，默认代码库后几位字母
* flags：获取的功能清单的模块，默认是service
* mockup：mock配置

* dependencies：需要排除的依赖，模块会帮助配置esl，sdk则需要依赖模块的配置
* public: boolean，build 时 dependencies 资源是CDN化还是本地私有化
* debug：debug中可设置dependencies中的某一个包使用指定的联调地址，便于依赖包本地联调

* proxyTarget：代理地址
* resourceBasePath：资源根路径，主要用于指定已集成到项目部署包中的公共资源访问根路径，production模式下默认为空，development模式下默认为项目代理地址，例如吉利现场环境的访问根路径为“https://console.cloud.geely.com”
* webpack：自定义webpack，可选，默认一个入口 index.js/index.ts

### 1.1.6-beta.6 版本

```
    1、uglifyjs-webpack-plugin 这个做混淆压缩，不支持ES2015；版本更新：如果项目中自定义webpack中配置了optimization，就不用uglifyjs-webpack-plugin
```


您可以通过init命令初始化自己的项目，将为您生成对应的bce-config.js。

### 项目初始化

```
    cd ${项目仓库目录}
    npx bce-cli init
    // ...根据提示进行操作
```

### 项目启动

```
    // 调试产品模块
    npx bce-cli dev

    // 调试产品模块，指定模板和配置
    npx bce-cli dev --template=public-console --config=bce-config.js // 产品模块

    // 调试工具模块
    npx bce-cli dev --sdk --config=bce-config.js

    // or package.json script
    npm run dev
```

### 本地mock

```
    npx bce-cli dev --mock --template=public-console --config=bce-config.js
```

### 项目构建

```
    // 构建产品模块
    npx bce-cli build --template=public-console --config=bce-config.js

    // 构建产品模块，指定模板和配置
    npx bce-cli build --template=public-console --config=bce-config.js

    // 构建工具模块
    npx bce-cli build --sdk --template=public-console --config=bce-config.js

    // or package.json script
    npm run build
```

### 获取依赖包列表

```
    npx bce-cli list --config=bce-config.js // 产品模块
    // or
    npx bce-cli list --sdk --config=bce-config.js // 工具模块

    // 将显示依赖包信息：
    // @baidu/bce-httpclient@******* ==> @baiducloud/fe-httpclient/*******/httpclient.js
```

### 检索指定的包

```
    npx bce-cli search @baiducloud/httpclient

    // 查找到以下安装包，添加到配置文件后重启即可完成引入:
    // '@baiducloud/httpclient': '@baiducloud/fe-httpclient/*******/httpclient.js'
```

### 生成功能清单开关声明文件(产品模块)

```
    npx bce-cli flag --gentypes --config=bce-config.js // 产品模块
    // 将在"项目目录/types/"下生成功能开关声明文件flag.d.ts
```
