{"name": "@baidu/bce-cli", "version": "1.1.3", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fcode-frame/-/code-frame-7.10.4.tgz", "integrity": "sha1-Fo2ho26Q2miujUnA8bSMfGJJITo=", "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/compat-data": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fcompat-data/-/compat-data-7.10.4.tgz", "integrity": "sha1-cGpkhO5vkQtxm2lqkZT42n16wkE=", "requires": {"browserslist": "^4.12.0", "invariant": "^2.2.4", "semver": "^5.5.0"}}, "@babel/core": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fcore/-/core-7.10.4.tgz", "integrity": "sha1-eA6Lg+SWFS+N199jiSsuBSvx1R0=", "requires": {"@babel/code-frame": "^7.10.4", "@babel/generator": "^7.10.4", "@babel/helper-module-transforms": "^7.10.4", "@babel/helpers": "^7.10.4", "@babel/parser": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.1", "json5": "^2.1.2", "lodash": "^4.17.13", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}}, "@babel/generator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fgenerator/-/generator-7.10.4.tgz", "integrity": "sha1-5J7u2f4RS2L6WxgYVqQ6XjL18kM=", "requires": {"@babel/types": "^7.10.4", "jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0"}}, "@babel/helper-annotate-as-pure": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-annotate-as-pure/-/helper-annotate-as-pure-7.10.4.tgz", "integrity": "sha1-W/DUlaP3V6w72ki1vzs7ownHK6M=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.10.4.tgz", "integrity": "sha1-uwt18xv5jL+f8UPBrleLhydK4aM=", "requires": {"@babel/helper-explode-assignable-expression": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helper-compilation-targets": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-compilation-targets/-/helper-compilation-targets-7.10.4.tgz", "integrity": "sha1-gEro4/BDdmB8x5G51H1UAnYzK9I=", "requires": {"@babel/compat-data": "^7.10.4", "browserslist": "^4.12.0", "invariant": "^2.2.4", "levenary": "^1.1.1", "semver": "^5.5.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.4.tgz", "integrity": "sha1-LUAV0BNr0xQQOnDYSnGD5LNEo1U=", "requires": {"@babel/helper-function-name": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.10.4.tgz", "integrity": "sha1-/dYNiFJGWaC2lZwFeZJeQlcU87g=", "requires": {"@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-regex": "^7.10.4", "regexpu-core": "^4.7.0"}}, "@babel/helper-define-map": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-define-map/-/helper-define-map-7.10.4.tgz", "integrity": "sha1-8DeteUJk9yntoYifTuIQuHCZkJI=", "requires": {"@babel/helper-function-name": "^7.10.4", "@babel/types": "^7.10.4", "lodash": "^4.17.13"}}, "@babel/helper-explode-assignable-expression": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-explode-assignable-expression/-/helper-explode-assignable-expression-7.10.4.tgz", "integrity": "sha1-QKHNkXv/Eoj2malKdbN6Gi29jHw=", "requires": {"@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helper-function-name": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-function-name/-/helper-function-name-7.10.4.tgz", "integrity": "sha1-0tOyDFmtjEcRL6fSqUvAnV74Lxo=", "requires": {"@babel/helper-get-function-arity": "^7.10.4", "@babel/template": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helper-get-function-arity": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-get-function-arity/-/helper-get-function-arity-7.10.4.tgz", "integrity": "sha1-mMHL6g4jMvM/mkZhuM4VBbLBm6I=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-hoist-variables": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-hoist-variables/-/helper-hoist-variables-7.10.4.tgz", "integrity": "sha1-1JsAHR1aaMpeZgTdoBpil/fJOB4=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-member-expression-to-functions/-/helper-member-expression-to-functions-7.10.4.tgz", "integrity": "sha1-fNBLV9/Pgvzprq59TkRS+jG4x8Q=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-module-imports": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-module-imports/-/helper-module-imports-7.10.4.tgz", "integrity": "sha1-TFxUvgS9MWcKc4J5fXW5+i5bViA=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-module-transforms": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-module-transforms/-/helper-module-transforms-7.10.4.tgz", "integrity": "sha1-yh8B/bhOSMJNdQa7gYyWHx2ogF0=", "requires": {"@babel/helper-module-imports": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-simple-access": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "@babel/template": "^7.10.4", "@babel/types": "^7.10.4", "lodash": "^4.17.13"}}, "@babel/helper-optimise-call-expression": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-optimise-call-expression/-/helper-optimise-call-expression-7.10.4.tgz", "integrity": "sha1-UNyWQT1ZT5lad5BZBbBYk813lnM=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-plugin-utils": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-plugin-utils/-/helper-plugin-utils-7.10.4.tgz", "integrity": "sha1-L3WoMSadT2d95JmG3/WZJ1M883U="}, "@babel/helper-regex": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-regex/-/helper-regex-7.10.4.tgz", "integrity": "sha1-WbNz2q80WOV0feznG7r0X5Z2r20=", "requires": {"lodash": "^4.17.13"}}, "@babel/helper-remap-async-to-generator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-remap-async-to-generator/-/helper-remap-async-to-generator-7.10.4.tgz", "integrity": "sha1-/Oi+pOlpC76SMFbe0h5UtOi2jtU=", "requires": {"@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-wrap-function": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helper-replace-supers": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-replace-supers/-/helper-replace-supers-7.10.4.tgz", "integrity": "sha1-1YXNk4jqBuYDHkzUS2cTy+rZ5s8=", "requires": {"@babel/helper-member-expression-to-functions": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helper-simple-access": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-simple-access/-/helper-simple-access-7.10.4.tgz", "integrity": "sha1-D1zNopRSd6KnotOoIeFTle3PNGE=", "requires": {"@babel/template": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helper-split-export-declaration": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-split-export-declaration/-/helper-split-export-declaration-7.10.4.tgz", "integrity": "sha1-LHBXbqo7VgmyTLmdsoiMw/xCUdE=", "requires": {"@babel/types": "^7.10.4"}}, "@babel/helper-validator-identifier": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.10.4.tgz", "integrity": "sha1-p4x6clHgH2FlEtMbEK3PUq2l4NI="}, "@babel/helper-wrap-function": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-wrap-function/-/helper-wrap-function-7.10.4.tgz", "integrity": "sha1-im9wHqsP8592W1oc/vQJmQ5iS4c=", "requires": {"@babel/helper-function-name": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/helpers": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelpers/-/helpers-7.10.4.tgz", "integrity": "sha1-Kr6w1yGv98Cpc3a54fb2XXpHUEQ=", "requires": {"@babel/template": "^7.10.4", "@babel/traverse": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/highlight": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhighlight/-/highlight-7.10.4.tgz", "integrity": "sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=", "requires": {"@babel/helper-validator-identifier": "^7.10.4", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fparser/-/parser-7.10.4.tgz", "integrity": "sha1-nu3yfhmY2Hc5+1AopRIFV8BqGmQ="}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.10.4.tgz", "integrity": "sha1-S2Wrs9m6zGxleqpBPlZpb58XD8Y=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-remap-async-to-generator": "^7.10.4", "@babel/plugin-syntax-async-generators": "^7.8.0"}}, "@babel/plugin-proposal-class-properties": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-class-properties/-/plugin-proposal-class-properties-7.10.4.tgz", "integrity": "sha1-ozv2Mto5ClnHqMVwBF0RFc13iAc=", "requires": {"@babel/helper-create-class-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-proposal-decorators": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-decorators/-/plugin-proposal-decorators-7.10.4.tgz", "integrity": "sha1-/iDvEMxz84b3CRD8pIeYBBzTV8c=", "requires": {"@babel/helper-create-class-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-decorators": "^7.10.4"}}, "@babel/plugin-proposal-dynamic-import": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.10.4.tgz", "integrity": "sha1-uleibLmLN3QenVvKG4sN34KR8X4=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}}, "@babel/plugin-proposal-json-strings": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-json-strings/-/plugin-proposal-json-strings-7.10.4.tgz", "integrity": "sha1-WT5ZxjUoFgIzvTIbGuvgggwjQds=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.0"}}, "@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.10.4.tgz", "integrity": "sha1-AqfpYfwy5tWy2wZJ4Bv4Dd7n4Eo=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.0"}}, "@babel/plugin-proposal-numeric-separator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.10.4.tgz", "integrity": "sha1-zhWQ/wplrRKXCmCdeIVemkwa7wY=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.10.4.tgz", "integrity": "sha1-UBKawha5pqVbOFP92SPnS/VTpMA=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.0", "@babel/plugin-transform-parameters": "^7.10.4"}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.10.4.tgz", "integrity": "sha1-Mck4MJ0kp4pJ1o/av/qoY3WFVN0=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-optional-catch-binding": "^7.8.0"}}, "@babel/plugin-proposal-optional-chaining": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.10.4.tgz", "integrity": "sha1-dQ8SVekwofgtjN3kUDH4Gg0K3/c=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-optional-chaining": "^7.8.0"}}, "@babel/plugin-proposal-private-methods": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-private-methods/-/plugin-proposal-private-methods-7.10.4.tgz", "integrity": "sha1-sWDZcrj9ulx9ERoUX8jEIfwqaQk=", "requires": {"@babel/helper-create-class-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.10.4.tgz", "integrity": "sha1-RIPNpTBBzjQTt/4vAAImZd36p10=", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-class-properties/-/plugin-syntax-class-properties-7.10.4.tgz", "integrity": "sha1-ZkTmoLqlWmH54yMfbJ7rbuRsEkw=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-decorators": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-decorators/-/plugin-syntax-decorators-7.10.4.tgz", "integrity": "sha1-aFMIWyxCn50yLQL1pjUBjN6yNgw=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "integrity": "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.10.4.tgz", "integrity": "sha1-S764kXtU/PdoNk4KgfVg4zo+9X0=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-typescript": {"version": "7.12.1", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-syntax-typescript/-/plugin-syntax-typescript-7.12.1.tgz", "integrity": "sha1-Rgup13B3ZTgDw90uZz921mtAKeU=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.10.4.tgz", "integrity": "sha1-4ilg135pfHT0HFAdRNc9v4pqZM0=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.10.4.tgz", "integrity": "sha1-QaUBfknrbzzak5KlHu8pQFskWjc=", "requires": {"@babel/helper-module-imports": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-remap-async-to-generator": "^7.10.4"}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.10.4.tgz", "integrity": "sha1-GvpZV0T3XkOpGvc7DZmOz+Trwug=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-block-scoping": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-block-scoping/-/plugin-transform-block-scoping-7.10.4.tgz", "integrity": "sha1-pnDRNku1AZpiG56iABSCh21zR4c=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "lodash": "^4.17.13"}}, "@babel/plugin-transform-classes": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-classes/-/plugin-transform-classes-7.10.4.tgz", "integrity": "sha1-QFE2rys+IYvEoZJiKLyRerGgrcc=", "requires": {"@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-define-map": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "globals": "^11.1.0"}}, "@babel/plugin-transform-computed-properties": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-computed-properties/-/plugin-transform-computed-properties-7.10.4.tgz", "integrity": "sha1-ne2DqBboLe0o1S1LTsvdgQzfwOs=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-destructuring": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-destructuring/-/plugin-transform-destructuring-7.10.4.tgz", "integrity": "sha1-cN3Ss9G+qD0BUJ6bsl3bOnT8heU=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-dotall-regex": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.10.4.tgz", "integrity": "sha1-RpwgYhBcHragQOr0+sS0iAeDle4=", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.10.4.tgz", "integrity": "sha1-aX5Qyf7hQ4D+hD0fMGspVhdDHkc=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.10.4.tgz", "integrity": "sha1-WuM4xX+M9AAb2zVgeuZrktZlry4=", "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-for-of": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-for-of/-/plugin-transform-for-of-7.10.4.tgz", "integrity": "sha1-wIiS6IGdOl2ykDGxFa9RHbv+uuk=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-function-name": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-function-name/-/plugin-transform-function-name-7.10.4.tgz", "integrity": "sha1-akZ4gOD8ljhRS6NpERgR3b4mRLc=", "requires": {"@babel/helper-function-name": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-literals": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-literals/-/plugin-transform-literals-7.10.4.tgz", "integrity": "sha1-n0K6CEEQChNfInEtDjkcRi9XHzw=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.10.4.tgz", "integrity": "sha1-sexE/PGVr8uNssYs2OVRyIG6+Lc=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-modules-amd": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-modules-amd/-/plugin-transform-modules-amd-7.10.4.tgz", "integrity": "sha1-y0B8aLhi5MHROi/HOMfsXtdfxSA=", "requires": {"@babel/helper-module-transforms": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "babel-plugin-dynamic-import-node": "^2.3.3"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.10.4.tgz", "integrity": "sha1-ZmZ8Pu2h6/eJbUHx8WsXEFovvKA=", "requires": {"@babel/helper-module-transforms": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-simple-access": "^7.10.4", "babel-plugin-dynamic-import-node": "^2.3.3"}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.4.tgz", "integrity": "sha1-j1dq/ZQ6wveJs13tCmMS+SnGM/k=", "requires": {"@babel/helper-hoist-variables": "^7.10.4", "@babel/helper-module-transforms": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "babel-plugin-dynamic-import-node": "^2.3.3"}}, "@babel/plugin-transform-modules-umd": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-modules-umd/-/plugin-transform-modules-umd-7.10.4.tgz", "integrity": "sha1-moSB/oG4JGVLOgtl2j34nz0hg54=", "requires": {"@babel/helper-module-transforms": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.10.4.tgz", "integrity": "sha1-eLTZeIELbzvPA/njGPL8DtQa7LY=", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.10.4"}}, "@babel/plugin-transform-new-target": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-new-target/-/plugin-transform-new-target-7.10.4.tgz", "integrity": "sha1-kJfXU8t7Aky3OBo7LlLpUTqcaIg=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-object-super": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-object-super/-/plugin-transform-object-super-7.10.4.tgz", "integrity": "sha1-1xRsTROUM+emUm+IjGZ+MUoJOJQ=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4"}}, "@babel/plugin-transform-parameters": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-parameters/-/plugin-transform-parameters-7.10.4.tgz", "integrity": "sha1-e00TfIfqetwqDz6/UyZocdqm/O0=", "requires": {"@babel/helper-get-function-arity": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-property-literals": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-property-literals/-/plugin-transform-property-literals-7.10.4.tgz", "integrity": "sha1-9v5UtlkDUimHhbg+3YFdIUxC48A=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-regenerator": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-regenerator/-/plugin-transform-regenerator-7.10.4.tgz", "integrity": "sha1-IBXlnYOQdOdoON4hWdtCGWb9i2M=", "requires": {"regenerator-transform": "^0.14.2"}}, "@babel/plugin-transform-reserved-words": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-reserved-words/-/plugin-transform-reserved-words-7.10.4.tgz", "integrity": "sha1-jyaCvNzvntMn4bCGFYXXAT+KVN0=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.10.4.tgz", "integrity": "sha1-n9Jexc3VVbt/Rz5ebuHJce7eTdY=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-spread": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-spread/-/plugin-transform-spread-7.10.4.tgz", "integrity": "sha1-TiyF6g1quu4bJNz7uuQm/o1nTP8=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.10.4.tgz", "integrity": "sha1-jziJ7oZXWBEwop2cyR18c7fEoo0=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-regex": "^7.10.4"}}, "@babel/plugin-transform-template-literals": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-template-literals/-/plugin-transform-template-literals-7.10.4.tgz", "integrity": "sha1-5jdUB7MPy3/P27o7uY7z6dNt97w=", "requires": {"@babel/helper-annotate-as-pure": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.10.4.tgz", "integrity": "sha1-lQnxp+7DHE7b/+E3wWzDP/C8W/w=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-typescript": {"version": "7.12.1", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-typescript/-/plugin-transform-typescript-7.12.1.tgz", "integrity": "sha1-2SzAr1BNUQ4mp1Sn28LlyM2cerQ=", "requires": {"@babel/helper-create-class-features-plugin": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-syntax-typescript": "^7.12.1"}, "dependencies": {"@babel/code-frame": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fcode-frame/-/code-frame-7.12.11.tgz", "integrity": "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=", "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/generator": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fgenerator/-/generator-7.12.11.tgz", "integrity": "sha1-mKffe4w1jJo3qweiQFaFMBaro68=", "requires": {"@babel/types": "^7.12.11", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.12.1", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-create-class-features-plugin/-/helper-create-class-features-plugin-7.12.1.tgz", "integrity": "sha1-PEWZj0Me3UqSFMXx060USKYTf24=", "requires": {"@babel/helper-function-name": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.1", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-replace-supers": "^7.12.1", "@babel/helper-split-export-declaration": "^7.10.4"}}, "@babel/helper-get-function-arity": {"version": "7.12.10", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-get-function-arity/-/helper-get-function-arity-7.12.10.tgz", "integrity": "sha1-sViBejFltfqiBHgl36YZcN3MFs8=", "requires": {"@babel/types": "^7.12.10"}}, "@babel/helper-member-expression-to-functions": {"version": "7.12.7", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-member-expression-to-functions/-/helper-member-expression-to-functions-7.12.7.tgz", "integrity": "sha1-qne9A5bsgRTl4weH76eFmdh0qFU=", "requires": {"@babel/types": "^7.12.7"}}, "@babel/helper-replace-supers": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-replace-supers/-/helper-replace-supers-7.12.11.tgz", "integrity": "sha1-6lEWWPxmx5CPkjEG3YjgjRmX1g0=", "requires": {"@babel/helper-member-expression-to-functions": "^7.12.7", "@babel/helper-optimise-call-expression": "^7.12.10", "@babel/traverse": "^7.12.10", "@babel/types": "^7.12.11"}, "dependencies": {"@babel/helper-optimise-call-expression": {"version": "7.12.10", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-optimise-call-expression/-/helper-optimise-call-expression-7.12.10.tgz", "integrity": "sha1-lMpOMG7hGn3W6fQoI+Ksa0mIHi0=", "requires": {"@babel/types": "^7.12.10"}}}}, "@babel/helper-validator-identifier": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.12.11.tgz", "integrity": "sha1-yaHwIZF9y1zPDU5FPjmQIpgfye0="}, "@babel/parser": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fparser/-/parser-7.12.11.tgz", "integrity": "sha1-nONZW810vFxGaQXobFNbiyUBHnk="}, "@babel/template": {"version": "7.12.7", "resolved": "http://registry.npm.baidu-int.com/@babel%2ftemplate/-/template-7.12.7.tgz", "integrity": "sha1-yBcjNpYBjjn7tsSR0vtoTgXtQ7w=", "requires": {"@babel/code-frame": "^7.10.4", "@babel/parser": "^7.12.7", "@babel/types": "^7.12.7"}}, "@babel/traverse": {"version": "7.12.12", "resolved": "http://registry.npm.baidu-int.com/@babel%2ftraverse/-/traverse-7.12.12.tgz", "integrity": "sha1-0M2HiScE7djaAC1nS8gRzmR0M3Y=", "requires": {"@babel/code-frame": "^7.12.11", "@babel/generator": "^7.12.11", "@babel/helper-function-name": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.11", "@babel/parser": "^7.12.11", "@babel/types": "^7.12.12", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.19"}, "dependencies": {"@babel/helper-function-name": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-function-name/-/helper-function-name-7.12.11.tgz", "integrity": "sha1-H9dziu5dz1PD7P8k8dqcUR7Ee0I=", "requires": {"@babel/helper-get-function-arity": "^7.12.10", "@babel/template": "^7.12.7", "@babel/types": "^7.12.11"}}, "@babel/helper-split-export-declaration": {"version": "7.12.11", "resolved": "http://registry.npm.baidu-int.com/@babel%2fhelper-split-export-declaration/-/helper-split-export-declaration-7.12.11.tgz", "integrity": "sha1-G0zEJEWGQ8R9NwIiI9oz126kYDo=", "requires": {"@babel/types": "^7.12.11"}}}}, "@babel/types": {"version": "7.12.12", "resolved": "http://registry.npm.baidu-int.com/@babel%2ftypes/-/types-7.12.12.tgz", "integrity": "sha1-Rgim7DE6u9h6+lUATTc60EqWwpk=", "requires": {"@babel/helper-validator-identifier": "^7.12.11", "lodash": "^4.17.19", "to-fast-properties": "^2.0.0"}}, "lodash": {"version": "4.17.20", "resolved": "http://registry.npm.baidu-int.com/lodash/-/lodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI="}}}, "@babel/plugin-transform-unicode-escapes": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.10.4.tgz", "integrity": "sha1-/q5SM5HHZR3awRXa4KnQaFeJIAc=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fplugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.10.4.tgz", "integrity": "sha1-5W1x+SgvrG2wnIJ0IFVXbV5tgKg=", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/preset-env": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fpreset-env/-/preset-env-7.10.4.tgz", "integrity": "sha1-+/V/moA6/Zf08y5PeYu2Lksr718=", "requires": {"@babel/compat-data": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-proposal-async-generator-functions": "^7.10.4", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-dynamic-import": "^7.10.4", "@babel/plugin-proposal-json-strings": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-numeric-separator": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.10.4", "@babel/plugin-proposal-optional-catch-binding": "^7.10.4", "@babel/plugin-proposal-optional-chaining": "^7.10.4", "@babel/plugin-proposal-private-methods": "^7.10.4", "@babel/plugin-proposal-unicode-property-regex": "^7.10.4", "@babel/plugin-syntax-async-generators": "^7.8.0", "@babel/plugin-syntax-class-properties": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0", "@babel/plugin-syntax-json-strings": "^7.8.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.0", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.0", "@babel/plugin-syntax-optional-catch-binding": "^7.8.0", "@babel/plugin-syntax-optional-chaining": "^7.8.0", "@babel/plugin-syntax-top-level-await": "^7.10.4", "@babel/plugin-transform-arrow-functions": "^7.10.4", "@babel/plugin-transform-async-to-generator": "^7.10.4", "@babel/plugin-transform-block-scoped-functions": "^7.10.4", "@babel/plugin-transform-block-scoping": "^7.10.4", "@babel/plugin-transform-classes": "^7.10.4", "@babel/plugin-transform-computed-properties": "^7.10.4", "@babel/plugin-transform-destructuring": "^7.10.4", "@babel/plugin-transform-dotall-regex": "^7.10.4", "@babel/plugin-transform-duplicate-keys": "^7.10.4", "@babel/plugin-transform-exponentiation-operator": "^7.10.4", "@babel/plugin-transform-for-of": "^7.10.4", "@babel/plugin-transform-function-name": "^7.10.4", "@babel/plugin-transform-literals": "^7.10.4", "@babel/plugin-transform-member-expression-literals": "^7.10.4", "@babel/plugin-transform-modules-amd": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.10.4", "@babel/plugin-transform-modules-systemjs": "^7.10.4", "@babel/plugin-transform-modules-umd": "^7.10.4", "@babel/plugin-transform-named-capturing-groups-regex": "^7.10.4", "@babel/plugin-transform-new-target": "^7.10.4", "@babel/plugin-transform-object-super": "^7.10.4", "@babel/plugin-transform-parameters": "^7.10.4", "@babel/plugin-transform-property-literals": "^7.10.4", "@babel/plugin-transform-regenerator": "^7.10.4", "@babel/plugin-transform-reserved-words": "^7.10.4", "@babel/plugin-transform-shorthand-properties": "^7.10.4", "@babel/plugin-transform-spread": "^7.10.4", "@babel/plugin-transform-sticky-regex": "^7.10.4", "@babel/plugin-transform-template-literals": "^7.10.4", "@babel/plugin-transform-typeof-symbol": "^7.10.4", "@babel/plugin-transform-unicode-escapes": "^7.10.4", "@babel/plugin-transform-unicode-regex": "^7.10.4", "@babel/preset-modules": "^0.1.3", "@babel/types": "^7.10.4", "browserslist": "^4.12.0", "core-js-compat": "^3.6.2", "invariant": "^2.2.2", "levenary": "^1.1.1", "semver": "^5.5.0"}}, "@babel/preset-modules": {"version": "0.1.3", "resolved": "http://registry.npm.baidu-int.com/@babel%2fpreset-modules/-/preset-modules-0.1.3.tgz", "integrity": "sha1-EyQrU7XvjIg8PPfd3VWzbOgPvHI=", "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}}, "@babel/preset-typescript": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fpreset-typescript/-/preset-typescript-7.10.4.tgz", "integrity": "sha1-fV0FLlKmgkgNbizFqjG+YcjCXjY=", "requires": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/plugin-transform-typescript": "^7.10.4"}}, "@babel/runtime": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2fruntime/-/runtime-7.10.4.tgz", "integrity": "sha1-pnJPGmuNL26lI22/5Yx9fqnF65k=", "requires": {"regenerator-runtime": "^0.13.4"}}, "@babel/template": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2ftemplate/-/template-7.10.4.tgz", "integrity": "sha1-MlGZbEIA68cdGo/EBfupQPNrong=", "requires": {"@babel/code-frame": "^7.10.4", "@babel/parser": "^7.10.4", "@babel/types": "^7.10.4"}}, "@babel/traverse": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2ftraverse/-/traverse-7.10.4.tgz", "integrity": "sha1-5kLlOVo7CcyVyOdKJ0MrSEtpeBg=", "requires": {"@babel/code-frame": "^7.10.4", "@babel/generator": "^7.10.4", "@babel/helper-function-name": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4", "@babel/parser": "^7.10.4", "@babel/types": "^7.10.4", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.13"}}, "@babel/types": {"version": "7.10.4", "resolved": "http://registry.npm.baidu-int.com/@babel%2ftypes/-/types-7.10.4.tgz", "integrity": "sha1-NpUXGINS4YIZmB79FWv9sZn/8e4=", "requires": {"@babel/helper-validator-identifier": "^7.10.4", "lodash": "^4.17.13", "to-fast-properties": "^2.0.0"}}, "@baiducloud/i18n": {"version": "1.0.0-rc.29", "resolved": "http://registry.npm.baidu-int.com/@baiducloud%2fi18n/-/i18n-1.0.0-rc.29.tgz", "integrity": "sha512-Tm6VRgS9OZKNXAJ1dJ/O514S1wV2rcPHqnWimlKosuYWxaTUsUvs4gHuJ1fNg2fD9Y2EBa7G8CTBQkY9wBkKNw==", "requires": {"@babel/core": "^7.6.4", "@babel/helper-module-imports": "^7.0.0", "@babel/parser": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.6.0", "@babel/plugin-proposal-optional-chaining": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/preset-env": "^7.6.3", "@babel/traverse": "^7.4.5", "commander": "^2.20.0", "core-js": "^3.3.6", "debug": "^4.1.1", "fs-walk": "0.0.2", "gettext-parser": "^1.3.0", "lodash": "^4.17.15", "minimatch": "^3.0.4", "mkdirp": "~0.5.0", "regenerator-runtime": "^0.13.3"}}, "@mrmlnc/readdir-enhanced": {"version": "2.2.1", "resolved": "http://registry.npm.baidu-int.com/@mrmlnc%2freaddir-enhanced/-/readdir-enhanced-2.2.1.tgz", "integrity": "sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g==", "requires": {"call-me-maybe": "^1.0.1", "glob-to-regexp": "^0.3.0"}}, "@nodelib/fs.stat": {"version": "1.1.3", "resolved": "http://registry.npm.baidu-int.com/@nodelib%2ffs.stat/-/fs.stat-1.1.3.tgz", "integrity": "sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw=="}, "@sindresorhus/is": {"version": "0.7.0", "resolved": "http://registry.npm.baidu-int.com/@sindresorhus%2fis/-/is-0.7.0.tgz", "integrity": "sha512-ONhaKPIufzzrlNbqtWFFd+jlnemX6lJAgq9ZeiZtS7I1PIf/la7CW4m83rTXRnVnsMbW2k56pGYu7AUFJD9Pow=="}, "@types/anymatch": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/@types%2fanymatch/-/anymatch-1.3.1.tgz", "integrity": "sha1-M2utwb7sudrMOL6izzKt9ieoQho="}, "@types/glob": {"version": "7.1.2", "resolved": "http://registry.npm.baidu-int.com/@types%2fglob/-/glob-7.1.2.tgz", "integrity": "sha1-BsomUhNTpUXZSgrcdPOKWdIyyYc=", "requires": {"@types/minimatch": "*", "@types/node": "*"}}, "@types/html-minifier-terser": {"version": "5.1.0", "resolved": "http://registry.npm.baidu-int.com/@types%2fhtml-minifier-terser/-/html-minifier-terser-5.1.0.tgz", "integrity": "sha1-VRpFibbuLMnB3/CAVhKK7Cm5SIA="}, "@types/json-schema": {"version": "7.0.5", "resolved": "http://registry.npm.baidu-int.com/@types%2fjson-schema/-/json-schema-7.0.5.tgz", "integrity": "sha1-3M5EMOZLRDuolF8CkPtWStW6xt0="}, "@types/json5": {"version": "0.0.29", "resolved": "http://registry.npm.baidu-int.com/@types%2fjson5/-/json5-0.0.29.tgz", "integrity": "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="}, "@types/minimatch": {"version": "3.0.3", "resolved": "http://registry.npm.baidu-int.com/@types%2fminimatch/-/minimatch-3.0.3.tgz", "integrity": "sha512-tHq6qdbT9U1IRSGf14CL0pUlULksvY9OZ+5eEgl1N7t+OA3tGvNpxJCzuKQlsNgCVwbAs670L1vcVQi8j9HjnA=="}, "@types/node": {"version": "14.0.14", "resolved": "http://registry.npm.baidu-int.com/@types%2fnode/-/node-14.0.14.tgz", "integrity": "sha1-JKC1lZ8WrBQa6wxbPNehW3xky84="}, "@types/q": {"version": "1.5.4", "resolved": "http://registry.npm.baidu-int.com/@types%2fq/-/q-1.5.4.tgz", "integrity": "sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ="}, "@types/source-list-map": {"version": "0.1.2", "resolved": "http://registry.npm.baidu-int.com/@types%2fsource-list-map/-/source-list-map-0.1.2.tgz", "integrity": "sha512-K5K+yml8LTo9bWJI/rECfIPrGgxdpeNbj+d53lwN4QjW1MCwlkhUms+gtdzigTeUyBr09+u8BwOIY3MXvHdcsA=="}, "@types/tapable": {"version": "1.0.6", "resolved": "http://registry.npm.baidu-int.com/@types%2ftapable/-/tapable-1.0.6.tgz", "integrity": "sha1-qcpLcKGLJwzLK8Cqr+/R1Ia36nQ="}, "@types/uglify-js": {"version": "3.9.2", "resolved": "http://registry.npm.baidu-int.com/@types%2fuglify-js/-/uglify-js-3.9.2.tgz", "integrity": "sha1-AZkled67pnTh41nNa8saHQqy4Cs=", "requires": {"source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "@types/webpack": {"version": "4.41.18", "resolved": "http://registry.npm.baidu-int.com/@types%2fwebpack/-/webpack-4.41.18.tgz", "integrity": "sha1-KUUgJheGbs3/pYIIfxtt4Ep+7VU=", "requires": {"@types/anymatch": "*", "@types/node": "*", "@types/tapable": "*", "@types/uglify-js": "*", "@types/webpack-sources": "*", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "@types/webpack-sources": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/@types%2fwebpack-sources/-/webpack-sources-1.4.0.tgz", "integrity": "sha1-5Y8fBfh9OaXGTPhXBb29u5TU1X4=", "requires": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.7.3"}, "dependencies": {"source-map": {"version": "0.7.3", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.7.3.tgz", "integrity": "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ=="}}}, "@webassemblyjs/ast": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fast/-/ast-1.9.0.tgz", "integrity": "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=", "requires": {"@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2ffloating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz", "integrity": "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q="}, "@webassemblyjs/helper-api-error": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-api-error/-/helper-api-error-1.9.0.tgz", "integrity": "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI="}, "@webassemblyjs/helper-buffer": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-buffer/-/helper-buffer-1.9.0.tgz", "integrity": "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA="}, "@webassemblyjs/helper-code-frame": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-code-frame/-/helper-code-frame-1.9.0.tgz", "integrity": "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=", "requires": {"@webassemblyjs/wast-printer": "1.9.0"}}, "@webassemblyjs/helper-fsm": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-fsm/-/helper-fsm-1.9.0.tgz", "integrity": "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg="}, "@webassemblyjs/helper-module-context": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-module-context/-/helper-module-context-1.9.0.tgz", "integrity": "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=", "requires": {"@webassemblyjs/ast": "1.9.0"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz", "integrity": "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A="}, "@webassemblyjs/helper-wasm-section": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fhelper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "integrity": "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0"}}, "@webassemblyjs/ieee754": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fieee754/-/ieee754-1.9.0.tgz", "integrity": "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=", "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fleb128/-/leb128-1.9.0.tgz", "integrity": "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=", "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2futf8/-/utf8-1.9.0.tgz", "integrity": "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas="}, "@webassemblyjs/wasm-edit": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fwasm-edit/-/wasm-edit-1.9.0.tgz", "integrity": "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0"}}, "@webassemblyjs/wasm-gen": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fwasm-gen/-/wasm-gen-1.9.0.tgz", "integrity": "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "@webassemblyjs/wasm-opt": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fwasm-opt/-/wasm-opt-1.9.0.tgz", "integrity": "sha1-IhEYHlsxMmRDzIES658LkChyGmE=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0"}}, "@webassemblyjs/wasm-parser": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fwasm-parser/-/wasm-parser-1.9.0.tgz", "integrity": "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "@webassemblyjs/wast-parser": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fwast-parser/-/wast-parser-1.9.0.tgz", "integrity": "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/floating-point-hex-parser": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-code-frame": "1.9.0", "@webassemblyjs/helper-fsm": "1.9.0", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/wast-printer": {"version": "1.9.0", "resolved": "http://registry.npm.baidu-int.com/@webassemblyjs%2fwast-printer/-/wast-printer-1.9.0.tgz", "integrity": "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/@xtuc%2fieee754/-/ieee754-1.2.0.tgz", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="}, "@xtuc/long": {"version": "4.2.2", "resolved": "http://registry.npm.baidu-int.com/@xtuc%2flong/-/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="}, "accepts": {"version": "1.3.7", "resolved": "http://registry.npm.baidu-int.com/accepts/-/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "acorn": {"version": "7.3.1", "resolved": "http://registry.npm.baidu-int.com/acorn/-/acorn-7.3.1.tgz", "integrity": "sha1-hQEHVNtTw/uvO56j4IOqXF0Uf/0="}, "acorn-jsx": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/acorn-jsx/-/acorn-jsx-5.2.0.tgz", "integrity": "sha1-TGYGkXPW/daO2FI5/CViJhgrLr4=", "dev": true}, "acorn-loose": {"version": "7.1.0", "resolved": "http://registry.npm.baidu-int.com/acorn-loose/-/acorn-loose-7.1.0.tgz", "integrity": "sha1-LIfcccDKdZ7CoRDu5DpQV8B66Fc=", "requires": {"acorn": "^7.0.0"}}, "acorn-walk": {"version": "7.2.0", "resolved": "http://registry.npm.baidu-int.com/acorn-walk/-/acorn-walk-7.2.0.tgz", "integrity": "sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w="}, "add-asset-html-webpack-plugin": {"version": "3.1.3", "resolved": "http://registry.npm.baidu-int.com/add-asset-html-webpack-plugin/-/add-asset-html-webpack-plugin-3.1.3.tgz", "integrity": "sha1-vsd9yBgmRKWKDuvK59H56xYtr98=", "requires": {"globby": "^9.0.0", "micromatch": "^3.1.3", "p-each-series": "^1.0.0"}}, "ajv": {"version": "6.12.2", "resolved": "http://registry.npm.baidu-int.com/ajv/-/ajv-6.12.2.tgz", "integrity": "sha1-xinF7O0XuvMUQ3kY0tqIyZ1ZWM0=", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-errors": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/ajv-errors/-/ajv-errors-1.0.1.tgz", "integrity": "sha1-81mGrOuRr63sQQL72FAUlQzvpk0="}, "ajv-keywords": {"version": "3.5.0", "resolved": "http://registry.npm.baidu-int.com/ajv-keywords/-/ajv-keywords-3.5.0.tgz", "integrity": "sha1-XIlFNwmHhZJtceaWEUpTznaO13M="}, "alphanum-sort": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/alphanum-sort/-/alphanum-sort-1.0.2.tgz", "integrity": "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="}, "ansi-colors": {"version": "3.2.4", "resolved": "http://registry.npm.baidu-int.com/ansi-colors/-/ansi-colors-3.2.4.tgz", "integrity": "sha1-46PaS/uubIapwoViXeEkojQCb78="}, "ansi-escapes": {"version": "3.2.0", "resolved": "http://registry.npm.baidu-int.com/ansi-escapes/-/ansi-escapes-3.2.0.tgz", "integrity": "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=", "dev": true}, "ansi-html": {"version": "0.0.7", "resolved": "http://registry.npm.baidu-int.com/ansi-html/-/ansi-html-0.0.7.tgz", "integrity": "sha1-gTWEAhliqenm/QOflA0S9WynhZ4="}, "ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "ansi-styles": {"version": "3.2.1", "resolved": "http://registry.npm.baidu-int.com/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/anymatch/-/anymatch-3.1.1.tgz", "integrity": "sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg==", "optional": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "aproba": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/aproba/-/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo="}, "archive-type": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/archive-type/-/archive-type-4.0.0.tgz", "integrity": "sha1-+S5yIzBW38aWlHJ0nCZ72wRrHXA=", "requires": {"file-type": "^4.2.0"}, "dependencies": {"file-type": {"version": "4.4.0", "resolved": "http://registry.npm.baidu-int.com/file-type/-/file-type-4.4.0.tgz", "integrity": "sha1-G2AOX8ofvcboDApwxxyNul95BsU="}}}, "argparse": {"version": "1.0.10", "resolved": "http://registry.npm.baidu-int.com/argparse/-/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "requires": {"sprintf-js": "~1.0.2"}}, "arr-diff": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="}, "arr-flatten": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="}, "arr-union": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="}, "array-flatten": {"version": "2.1.2", "resolved": "http://registry.npm.baidu-int.com/array-flatten/-/array-flatten-2.1.2.tgz", "integrity": "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="}, "array-includes": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/array-includes/-/array-includes-3.1.1.tgz", "integrity": "sha1-zdZ+aFK9+cEhVGB4ZzIlXtJFk0g=", "dev": true, "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0", "is-string": "^1.0.5"}}, "array-union": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/array-union/-/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "requires": {"array-uniq": "^1.0.1"}}, "array-uniq": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY="}, "array-unique": {"version": "0.3.2", "resolved": "http://registry.npm.baidu-int.com/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="}, "array.prototype.flat": {"version": "1.2.3", "resolved": "http://registry.npm.baidu-int.com/array.prototype.flat/-/array.prototype.flat-1.2.3.tgz", "integrity": "sha1-DegrQmsDGNv9uUAInjiwQ9N/bHs=", "dev": true, "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1"}}, "asap": {"version": "2.0.6", "resolved": "http://registry.npm.baidu-int.com/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "optional": true}, "asn1": {"version": "0.2.4", "resolved": "http://registry.npm.baidu-int.com/asn1/-/asn1-0.2.4.tgz", "integrity": "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==", "optional": true, "requires": {"safer-buffer": "~2.1.0"}}, "asn1.js": {"version": "4.10.1", "resolved": "http://registry.npm.baidu-int.com/asn1.js/-/asn1.js-4.10.1.tgz", "integrity": "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=", "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "assert": {"version": "1.5.0", "resolved": "http://registry.npm.baidu-int.com/assert/-/assert-1.5.0.tgz", "integrity": "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=", "requires": {"object-assign": "^4.1.1", "util": "0.10.3"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE="}, "util": {"version": "0.10.3", "resolved": "http://registry.npm.baidu-int.com/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "requires": {"inherits": "2.0.1"}}}}, "assert-plus": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "optional": true}, "assign-symbols": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="}, "astral-regex": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/astral-regex/-/astral-regex-1.0.0.tgz", "integrity": "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=", "dev": true}, "async": {"version": "2.6.3", "resolved": "http://registry.npm.baidu-int.com/async/-/async-2.6.3.tgz", "integrity": "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==", "requires": {"lodash": "^4.17.14"}}, "async-each": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/async-each/-/async-each-1.0.3.tgz", "integrity": "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8="}, "async-limiter": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/async-limiter/-/async-limiter-1.0.1.tgz", "integrity": "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ=="}, "asynckit": {"version": "0.4.0", "resolved": "http://registry.npm.baidu-int.com/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "optional": true}, "atob": {"version": "2.1.2", "resolved": "http://registry.npm.baidu-int.com/atob/-/atob-2.1.2.tgz", "integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="}, "autoprefixer": {"version": "9.8.4", "resolved": "http://registry.npm.baidu-int.com/autoprefixer/-/autoprefixer-9.8.4.tgz", "integrity": "sha1-c28QEmc6cPo0ZGcdeNQavVRRKGM=", "requires": {"browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001087", "colorette": "^1.2.0", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "postcss": "^7.0.32", "postcss-value-parser": "^4.1.0"}}, "aws4": {"version": "1.10.0", "resolved": "http://registry.npm.baidu-int.com/aws4/-/aws4-1.10.0.tgz", "integrity": "sha1-oXs6jqgRBg501H0wYSJACtRJeuI=", "optional": true}, "babel-code-frame": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-code-frame/-/babel-code-frame-7.0.0-beta.0.tgz", "integrity": "sha1-QYp7Xz99yaRnDmGxFYtMVmG+yY0=", "dev": true, "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "dependencies": {"js-tokens": {"version": "3.0.2", "resolved": "http://registry.npm.baidu-int.com/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}}}, "babel-eslint": {"version": "8.0.1", "resolved": "http://registry.npm.baidu-int.com/babel-eslint/-/babel-eslint-8.0.1.tgz", "integrity": "sha1-XXGL56MoYl0AYCLrKT7TAIy9Y0Y=", "dev": true, "requires": {"babel-code-frame": "7.0.0-beta.0", "babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0", "babylon": "7.0.0-beta.22"}}, "babel-helper-function-name": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-helper-function-name/-/babel-helper-function-name-7.0.0-beta.0.tgz", "integrity": "sha1-0bZ3m2R+XFwx6+sF4TuZjk01LVY=", "dev": true, "requires": {"babel-helper-get-function-arity": "7.0.0-beta.0", "babel-template": "7.0.0-beta.0", "babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0"}}, "babel-helper-get-function-arity": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-helper-get-function-arity/-/babel-helper-get-function-arity-7.0.0-beta.0.tgz", "integrity": "sha1-nRq3ITu17+HvFjio6hSJlptai24=", "dev": true, "requires": {"babel-types": "7.0.0-beta.0"}}, "babel-loader": {"version": "8.1.0", "resolved": "http://registry.npm.baidu-int.com/babel-loader/-/babel-loader-8.1.0.tgz", "integrity": "sha1-xhHVESvVIJq+i5+oTD5NolJ18cM=", "requires": {"find-cache-dir": "^2.1.0", "loader-utils": "^1.4.0", "mkdirp": "^0.5.3", "pify": "^4.0.1", "schema-utils": "^2.6.5"}}, "babel-messages": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-messages/-/babel-messages-7.0.0-beta.0.tgz", "integrity": "sha1-bfASluSfyPvQY3OUMmoWfzbagXs=", "dev": true}, "babel-plugin-dynamic-import-node": {"version": "2.3.3", "resolved": "http://registry.npm.baidu-int.com/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz", "integrity": "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=", "requires": {"object.assign": "^4.1.0"}}, "babel-template": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-template/-/babel-template-7.0.0-beta.0.tgz", "integrity": "sha1-hQg8+eQ5XV5Iv1FU16jWmRyv7Ps=", "dev": true, "requires": {"babel-traverse": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0", "babylon": "7.0.0-beta.22", "lodash": "^4.2.0"}}, "babel-traverse": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-traverse/-/babel-traverse-7.0.0-beta.0.tgz", "integrity": "sha1-2hS+m3YvYqLwYNtGTqr92M0HKkE=", "dev": true, "requires": {"babel-code-frame": "7.0.0-beta.0", "babel-helper-function-name": "7.0.0-beta.0", "babel-messages": "7.0.0-beta.0", "babel-types": "7.0.0-beta.0", "babylon": "7.0.0-beta.22", "debug": "^3.0.1", "globals": "^10.0.0", "invariant": "^2.2.0", "lodash": "^4.2.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "dev": true, "requires": {"ms": "^2.1.1"}}, "globals": {"version": "10.4.0", "resolved": "http://registry.npm.baidu-int.com/globals/-/globals-10.4.0.tgz", "integrity": "sha1-XEdziLEoqeTFxdAceirKaMaLLac=", "dev": true}}}, "babel-types": {"version": "7.0.0-beta.0", "resolved": "http://registry.npm.baidu-int.com/babel-types/-/babel-types-7.0.0-beta.0.tgz", "integrity": "sha1-64tuVWRw5tzErvmC15rSKUabUWk=", "dev": true, "requires": {"esutils": "^2.0.2", "lodash": "^4.2.0", "to-fast-properties": "^2.0.0"}}, "babylon": {"version": "7.0.0-beta.22", "resolved": "http://registry.npm.baidu-int.com/babylon/-/babylon-7.0.0-beta.22.tgz", "integrity": "sha512-Yl7iT8QGrS8OfR7p6R12AJexQm+brKwrryai4VWZ7NHUbPoZ5al3+klhvl/14shXZiLa7uK//OIFuZ1/RKHgoA==", "dev": true}, "balanced-match": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "base": {"version": "0.11.2", "resolved": "http://registry.npm.baidu-int.com/base/-/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/base64-js/-/base64-js-1.3.1.tgz", "integrity": "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g=="}, "batch": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/batch/-/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "big-integer": {"version": "1.6.48", "resolved": "http://registry.npm.baidu-int.com/big-integer/-/big-integer-1.6.48.tgz", "integrity": "sha512-j51egjPa7/i+RdiRuJbPdJ2FIUYYPhvYLjzoYbcMMm62ooO6F94fETG4MTs46zPAF9Brs04OajboA/qTGuz78w=="}, "big.js": {"version": "5.2.2", "resolved": "http://registry.npm.baidu-int.com/big.js/-/big.js-5.2.2.tgz", "integrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="}, "binary": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/binary/-/binary-0.3.0.tgz", "integrity": "sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk=", "requires": {"buffers": "~0.1.1", "chainsaw": "~0.1.0"}}, "binary-extensions": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/binary-extensions/-/binary-extensions-2.1.0.tgz", "integrity": "sha1-MPpAyef+B9vIlWeM0ocCTeokHdk=", "optional": true}, "bindings": {"version": "1.5.0", "resolved": "http://registry.npm.baidu-int.com/bindings/-/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "optional": true, "requires": {"file-uri-to-path": "1.0.0"}}, "bl": {"version": "1.2.2", "resolved": "http://registry.npm.baidu-int.com/bl/-/bl-1.2.2.tgz", "integrity": "sha1-oWCRFxcQPAdBDO9j71Gzl8Alr5w=", "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "bluebird": {"version": "3.7.2", "resolved": "http://registry.npm.baidu-int.com/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="}, "bn.js": {"version": "5.1.2", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-5.1.2.tgz", "integrity": "sha1-yWhpAtPJoncp9DqxD515wgBNp7A="}, "body-parser": {"version": "1.19.0", "resolved": "http://registry.npm.baidu-int.com/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=", "requires": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "dependencies": {"bytes": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "qs": {"version": "6.7.0", "resolved": "http://registry.npm.baidu-int.com/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="}}}, "bonjour": {"version": "3.5.0", "resolved": "http://registry.npm.baidu-int.com/bonjour/-/bonjour-3.5.0.tgz", "integrity": "sha1-jokKGD2O6aI5OzhExpGkK897yfU=", "requires": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "boolbase": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24="}, "boom": {"version": "2.10.1", "resolved": "http://registry.npm.baidu-int.com/boom/-/boom-2.10.1.tgz", "integrity": "sha1-OciRjO/1eZ+D+UkqhI9iWt0Mdm8=", "optional": true, "requires": {"hoek": "2.x.x"}}, "brace-expansion": {"version": "1.1.11", "resolved": "http://registry.npm.baidu-int.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "http://registry.npm.baidu-int.com/braces/-/braces-2.3.2.tgz", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "brorand": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8="}, "browserify-aes": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==", "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==", "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/browserify-des/-/browserify-des-1.0.2.tgz", "integrity": "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==", "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "browserify-rsa": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/browserify-rsa/-/browserify-rsa-4.0.1.tgz", "integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "requires": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "browserify-sign": {"version": "4.2.0", "resolved": "http://registry.npm.baidu-int.com/browserify-sign/-/browserify-sign-4.2.0.tgz", "integrity": "sha1-VF0LGwfmssmSEQgr8bEsznoLDhE=", "requires": {"bn.js": "^5.1.1", "browserify-rsa": "^4.0.1", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.2", "inherits": "^2.0.4", "parse-asn1": "^5.1.5", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://registry.npm.baidu-int.com/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://registry.npm.baidu-int.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="}}}, "browserify-zlib": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "requires": {"pako": "~1.0.5"}}, "browserslist": {"version": "4.12.2", "resolved": "http://registry.npm.baidu-int.com/browserslist/-/browserslist-4.12.2.tgz", "integrity": "sha1-dmU9fkxXyqihooUT4vThl9wRpxE=", "requires": {"caniuse-lite": "^1.0.30001088", "electron-to-chromium": "^1.3.483", "escalade": "^3.0.1", "node-releases": "^1.1.58"}}, "buffer": {"version": "5.6.0", "resolved": "http://registry.npm.baidu-int.com/buffer/-/buffer-5.6.0.tgz", "integrity": "sha1-oxdJ3H2B2E2wir+Te2uMQDP2J4Y=", "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}}, "buffer-alloc": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/buffer-alloc/-/buffer-alloc-1.2.0.tgz", "integrity": "sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==", "requires": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "buffer-alloc-unsafe": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz", "integrity": "sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg=="}, "buffer-crc32": {"version": "0.2.13", "resolved": "http://registry.npm.baidu-int.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="}, "buffer-fill": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/buffer-fill/-/buffer-fill-1.0.0.tgz", "integrity": "sha1-+PeLdniYiO858gXNY39o5wISKyw="}, "buffer-from": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A=="}, "buffer-indexof": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/buffer-indexof/-/buffer-indexof-1.1.1.tgz", "integrity": "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="}, "buffer-indexof-polyfill": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/buffer-indexof-polyfill/-/buffer-indexof-polyfill-1.0.1.tgz", "integrity": "sha1-qfuAbOgUXVQoUQznLyeLs2OmOL8="}, "buffer-shims": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/buffer-shims/-/buffer-shims-1.0.0.tgz", "integrity": "sha1-mXjOMXOIxkmth5MCjDR37wRKi1E="}, "buffer-xor": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk="}, "buffers": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/buffers/-/buffers-0.1.1.tgz", "integrity": "sha1-skV5w77U1tOWru5tmorn9Ugqt7s="}, "builtin-status-codes": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug="}, "bytes": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="}, "cacache": {"version": "12.0.4", "resolved": "http://registry.npm.baidu-int.com/cacache/-/cacache-12.0.4.tgz", "integrity": "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=", "requires": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "cache-base": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "cacheable-request": {"version": "2.1.4", "resolved": "http://registry.npm.baidu-int.com/cacheable-request/-/cacheable-request-2.1.4.tgz", "integrity": "sha1-DYCIAbY0KtM8kd+dC0TcCbkeXD0=", "requires": {"clone-response": "1.0.2", "get-stream": "3.0.0", "http-cache-semantics": "3.8.1", "keyv": "3.0.0", "lowercase-keys": "1.0.0", "normalize-url": "2.0.1", "responselike": "1.0.2"}, "dependencies": {"lowercase-keys": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/lowercase-keys/-/lowercase-keys-1.0.0.tgz", "integrity": "sha1-TjNms55/VFfjXxMkvfb4jQv8cwY="}, "normalize-url": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/normalize-url/-/normalize-url-2.0.1.tgz", "integrity": "sha1-g1qdoVUfom9w6SMpBpojqmV01+Y=", "requires": {"prepend-http": "^2.0.0", "query-string": "^5.0.1", "sort-keys": "^2.0.0"}}, "sort-keys": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/sort-keys/-/sort-keys-2.0.0.tgz", "integrity": "sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg=", "requires": {"is-plain-obj": "^1.0.0"}}}}, "call-me-maybe": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/call-me-maybe/-/call-me-maybe-1.0.1.tgz", "integrity": "sha1-JtII6onje1y95gJQoV8DHBak1ms="}, "caller-callsite": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/caller-callsite/-/caller-callsite-2.0.0.tgz", "integrity": "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=", "requires": {"callsites": "^2.0.0"}}, "caller-path": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/caller-path/-/caller-path-2.0.0.tgz", "integrity": "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=", "requires": {"caller-callsite": "^2.0.0"}}, "callsites": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/callsites/-/callsites-2.0.0.tgz", "integrity": "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="}, "camel-case": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/camel-case/-/camel-case-4.1.1.tgz", "integrity": "sha1-H8QchU8A4vfQE53+uhVC1olv5Uc=", "requires": {"pascal-case": "^3.1.1", "tslib": "^1.10.0"}}, "camelcase": {"version": "5.3.1", "resolved": "http://registry.npm.baidu-int.com/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="}, "caniuse-api": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/caniuse-api/-/caniuse-api-3.0.0.tgz", "integrity": "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==", "requires": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "caniuse-lite": {"version": "1.0.30001093", "resolved": "http://registry.npm.baidu-int.com/caniuse-lite/-/caniuse-lite-1.0.30001093.tgz", "integrity": "sha1-gz6A9ksaBFXLzu0qSjuvGeSr0xI="}, "caseless": {"version": "0.12.0", "resolved": "http://registry.npm.baidu-int.com/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "optional": true}, "caw": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/caw/-/caw-2.0.1.tgz", "integrity": "sha1-bDygcfwZRyCIPC3F2psHS/x+npU=", "requires": {"get-proxy": "^2.0.0", "isurl": "^1.0.0-alpha5", "tunnel-agent": "^0.6.0", "url-to-options": "^1.0.1"}}, "chainsaw": {"version": "0.1.0", "resolved": "http://registry.npm.baidu-int.com/chainsaw/-/chainsaw-0.1.0.tgz", "integrity": "sha1-XqtQsor+WAdNDVgpE4iCi15fvJg=", "requires": {"traverse": ">=0.3.0 <0.4"}}, "chalk": {"version": "2.4.2", "resolved": "http://registry.npm.baidu-int.com/chalk/-/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chardet": {"version": "0.7.0", "resolved": "http://registry.npm.baidu-int.com/chardet/-/chardet-0.7.0.tgz", "integrity": "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA=="}, "chokidar": {"version": "3.4.0", "resolved": "http://registry.npm.baidu-int.com/chokidar/-/chokidar-3.4.0.tgz", "integrity": "sha1-swYRQjzjdjV8dlubj5BLn7o8C+g=", "optional": true, "requires": {"anymatch": "~3.1.1", "braces": "~3.0.2", "fsevents": "~2.1.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.4.0"}, "dependencies": {"braces": {"version": "3.0.2", "resolved": "http://registry.npm.baidu-int.com/braces/-/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "optional": true, "requires": {"fill-range": "^7.0.1"}}, "fill-range": {"version": "7.0.1", "resolved": "http://registry.npm.baidu-int.com/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "optional": true, "requires": {"to-regex-range": "^5.0.1"}}, "glob-parent": {"version": "5.1.1", "resolved": "http://registry.npm.baidu-int.com/glob-parent/-/glob-parent-5.1.1.tgz", "integrity": "sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=", "optional": true, "requires": {"is-glob": "^4.0.1"}}, "is-number": {"version": "7.0.0", "resolved": "http://registry.npm.baidu-int.com/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "optional": true}, "to-regex-range": {"version": "5.0.1", "resolved": "http://registry.npm.baidu-int.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "optional": true, "requires": {"is-number": "^7.0.0"}}}}, "chownr": {"version": "1.1.4", "resolved": "http://registry.npm.baidu-int.com/chownr/-/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs="}, "chrome-trace-event": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz", "integrity": "sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ==", "requires": {"tslib": "^1.9.0"}}, "cipher-base": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/cipher-base/-/cipher-base-1.0.4.tgz", "integrity": "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=", "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "class-utils": {"version": "0.3.6", "resolved": "http://registry.npm.baidu-int.com/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "clean-css": {"version": "4.2.3", "resolved": "http://registry.npm.baidu-int.com/clean-css/-/clean-css-4.2.3.tgz", "integrity": "sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g=", "requires": {"source-map": "~0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "cli-cursor": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/cli-cursor/-/cli-cursor-3.1.0.tgz", "integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "requires": {"restore-cursor": "^3.1.0"}}, "cli-width": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/cli-width/-/cli-width-3.0.0.tgz", "integrity": "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY="}, "cliui": {"version": "5.0.0", "resolved": "http://registry.npm.baidu-int.com/cliui/-/cliui-5.0.0.tgz", "integrity": "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=", "requires": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="}, "strip-ansi": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "requires": {"ansi-regex": "^4.1.0"}}}}, "clone": {"version": "2.1.2", "resolved": "http://registry.npm.baidu-int.com/clone/-/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-response": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/clone-response/-/clone-response-1.0.2.tgz", "integrity": "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=", "requires": {"mimic-response": "^1.0.0"}}, "co": {"version": "4.6.0", "resolved": "http://registry.npm.baidu-int.com/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "optional": true}, "coa": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/coa/-/coa-2.0.2.tgz", "integrity": "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=", "requires": {"@types/q": "^1.5.1", "chalk": "^2.4.1", "q": "^1.1.2"}}, "collection-visit": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color": {"version": "3.1.2", "resolved": "http://registry.npm.baidu-int.com/color/-/color-3.1.2.tgz", "integrity": "sha512-vXTJhHebByxZn3lDvDJYw4lR5+uB3vuoHsuYA5AKuxRVn5wzzIfQKGLBmgdVRHKTJYeK5rvJcHnrd0Li49CFpg==", "requires": {"color-convert": "^1.9.1", "color-string": "^1.5.2"}}, "color-convert": {"version": "1.9.3", "resolved": "http://registry.npm.baidu-int.com/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://registry.npm.baidu-int.com/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "color-string": {"version": "1.5.3", "resolved": "http://registry.npm.baidu-int.com/color-string/-/color-string-1.5.3.tgz", "integrity": "sha512-dC2C5qeWoYkxki5UAXapdjqO672AM4vZuPGRQfO8b5HKuKGBbKWpITyDYN7TOFKvRW7kOgAn3746clDBMDJyQw==", "requires": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "colorette": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/colorette/-/colorette-1.2.0.tgz", "integrity": "sha1-RTBq3YJtGW6MhyNqwF15fyWYLmM="}, "combined-stream": {"version": "1.0.8", "resolved": "http://registry.npm.baidu-int.com/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "optional": true, "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.3", "resolved": "http://registry.npm.baidu-int.com/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "commondir": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="}, "component-emitter": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/component-emitter/-/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="}, "compressible": {"version": "2.0.18", "resolved": "http://registry.npm.baidu-int.com/compressible/-/compressible-2.0.18.tgz", "integrity": "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=", "requires": {"mime-db": ">= 1.43.0 < 2"}}, "compression": {"version": "1.7.4", "resolved": "http://registry.npm.baidu-int.com/compression/-/compression-1.7.4.tgz", "integrity": "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=", "requires": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "concat-map": {"version": "0.0.1", "resolved": "http://registry.npm.baidu-int.com/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "concat-stream": {"version": "1.6.2", "resolved": "http://registry.npm.baidu-int.com/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "config-chain": {"version": "1.1.12", "resolved": "http://registry.npm.baidu-int.com/config-chain/-/config-chain-1.1.12.tgz", "integrity": "sha512-a1eOIcu8+7lUInge4Rpf/n4Krkf3Dd9lqhljRzII1/Zno/kRtUWnznPO3jOKBmTEktkt3fkxisUcivoj0ebzoA==", "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "confusing-browser-globals": {"version": "1.0.9", "resolved": "http://registry.npm.baidu-int.com/confusing-browser-globals/-/confusing-browser-globals-1.0.9.tgz", "integrity": "sha512-KbS1Y0jMtyPgIxjO7ZzMAuUpAKMt1SzCL9fsrKsX6b0zJPTaT0SiSPmewwVZg9UAO83HVIlEhZF84LIjZ0lmAw==", "dev": true}, "connect-history-api-fallback": {"version": "1.6.0", "resolved": "http://registry.npm.baidu-int.com/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz", "integrity": "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="}, "console-browserify": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/console-browserify/-/console-browserify-1.2.0.tgz", "integrity": "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA=="}, "constants-browserify": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U="}, "contains-path": {"version": "0.1.0", "resolved": "http://registry.npm.baidu-int.com/contains-path/-/contains-path-0.1.0.tgz", "integrity": "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=", "dev": true}, "content-disposition": {"version": "0.5.3", "resolved": "http://registry.npm.baidu-int.com/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "requires": {"safe-buffer": "5.1.2"}}, "content-type": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "convert-source-map": {"version": "1.7.0", "resolved": "http://registry.npm.baidu-int.com/convert-source-map/-/convert-source-map-1.7.0.tgz", "integrity": "sha512-4FJkXzKXEDB1snCFZlLP4gpC3JILicCpGbzG9f9G7tGqGCzETQ2hWPrcinA9oU4wtf2biUaEH5065UnMeR33oA==", "requires": {"safe-buffer": "~5.1.1"}}, "cookie": {"version": "0.4.0", "resolved": "http://registry.npm.baidu-int.com/cookie/-/cookie-0.4.0.tgz", "integrity": "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="}, "cookie-signature": {"version": "1.0.6", "resolved": "http://registry.npm.baidu-int.com/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "copy-concurrently": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="}, "core-js": {"version": "3.6.5", "resolved": "http://registry.npm.baidu-int.com/core-js/-/core-js-3.6.5.tgz", "integrity": "sha1-c5XcJzrzf7LlDpvT2f6EEoUjHRo="}, "core-js-bundle": {"version": "3.6.5", "resolved": "http://registry.npm.baidu-int.com/core-js-bundle/-/core-js-bundle-3.6.5.tgz", "integrity": "sha1-OkJa1mrRmu7+qJrP1Iz/Z0/1hZA=", "dev": true}, "core-js-compat": {"version": "3.6.5", "resolved": "http://registry.npm.baidu-int.com/core-js-compat/-/core-js-compat-3.6.5.tgz", "integrity": "sha1-KlHZpOJd/W5pAlGqgfmePAVIHxw=", "requires": {"browserslist": "^4.8.5", "semver": "7.0.0"}, "dependencies": {"semver": {"version": "7.0.0", "resolved": "http://registry.npm.baidu-int.com/semver/-/semver-7.0.0.tgz", "integrity": "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44="}}}, "core-util-is": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "cosmiconfig": {"version": "5.2.1", "resolved": "http://registry.npm.baidu-int.com/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "integrity": "sha1-BA9yaAnFked6F8CjYmykW08Wixo=", "requires": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}}, "create-ecdh": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/create-ecdh/-/create-ecdh-4.0.3.tgz", "integrity": "sha512-GbEHQPMOswGpKXM9kCWVrremUcBmjteUaQ01T9rkKCPDXfUHX0IoP9LpHYo2NPFampa4e+/pFDc3jQdxrxQLaw==", "requires": {"bn.js": "^4.1.0", "elliptic": "^6.0.0"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "create-hash": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==", "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "http://registry.npm.baidu-int.com/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==", "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-spawn": {"version": "6.0.5", "resolved": "http://registry.npm.baidu-int.com/cross-spawn/-/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "cryptiles": {"version": "2.0.5", "resolved": "http://registry.npm.baidu-int.com/cryptiles/-/cryptiles-2.0.5.tgz", "integrity": "sha1-O9/s3GCBR8HGcgL6KR59ylnqo7g=", "optional": true, "requires": {"boom": "2.x.x"}}, "crypto-browserify": {"version": "3.12.0", "resolved": "http://registry.npm.baidu-int.com/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "integrity": "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=", "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "css-color-names": {"version": "0.0.4", "resolved": "http://registry.npm.baidu-int.com/css-color-names/-/css-color-names-0.0.4.tgz", "integrity": "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA="}, "css-declaration-sorter": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz", "integrity": "sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA==", "requires": {"postcss": "^7.0.1", "timsort": "^0.3.0"}}, "css-loader": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/css-loader/-/css-loader-2.1.1.tgz", "integrity": "sha1-2CVPcuQSuyI4u0TdZ0/770lzM+o=", "requires": {"camelcase": "^5.2.0", "icss-utils": "^4.1.0", "loader-utils": "^1.2.3", "normalize-path": "^3.0.0", "postcss": "^7.0.14", "postcss-modules-extract-imports": "^2.0.0", "postcss-modules-local-by-default": "^2.0.6", "postcss-modules-scope": "^2.1.0", "postcss-modules-values": "^2.0.0", "postcss-value-parser": "^3.3.0", "schema-utils": "^1.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}, "schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "css-select": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/css-select/-/css-select-2.1.0.tgz", "integrity": "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==", "requires": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}}, "css-select-base-adapter": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz", "integrity": "sha512-jQ<PERSON>eeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w=="}, "css-tree": {"version": "1.0.0-alpha.37", "resolved": "http://registry.npm.baidu-int.com/css-tree/-/css-tree-1.0.0-alpha.37.tgz", "integrity": "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==", "requires": {"mdn-data": "2.0.4", "source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "css-what": {"version": "3.3.0", "resolved": "http://registry.npm.baidu-int.com/css-what/-/css-what-3.3.0.tgz", "integrity": "sha1-EP7Glqns4uWRrHctdZqsq6w4zTk="}, "cssesc": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/cssesc/-/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="}, "cssnano": {"version": "4.1.10", "resolved": "http://registry.npm.baidu-int.com/cssnano/-/cssnano-4.1.10.tgz", "integrity": "sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI=", "requires": {"cosmiconfig": "^5.0.0", "cssnano-preset-default": "^4.0.7", "is-resolvable": "^1.0.0", "postcss": "^7.0.0"}}, "cssnano-preset-default": {"version": "4.0.7", "resolved": "http://registry.npm.baidu-int.com/cssnano-preset-default/-/cssnano-preset-default-4.0.7.tgz", "integrity": "sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y=", "requires": {"css-declaration-sorter": "^4.0.1", "cssnano-util-raw-cache": "^4.0.1", "postcss": "^7.0.0", "postcss-calc": "^7.0.1", "postcss-colormin": "^4.0.3", "postcss-convert-values": "^4.0.1", "postcss-discard-comments": "^4.0.2", "postcss-discard-duplicates": "^4.0.2", "postcss-discard-empty": "^4.0.1", "postcss-discard-overridden": "^4.0.1", "postcss-merge-longhand": "^4.0.11", "postcss-merge-rules": "^4.0.3", "postcss-minify-font-values": "^4.0.2", "postcss-minify-gradients": "^4.0.2", "postcss-minify-params": "^4.0.2", "postcss-minify-selectors": "^4.0.2", "postcss-normalize-charset": "^4.0.1", "postcss-normalize-display-values": "^4.0.2", "postcss-normalize-positions": "^4.0.2", "postcss-normalize-repeat-style": "^4.0.2", "postcss-normalize-string": "^4.0.2", "postcss-normalize-timing-functions": "^4.0.2", "postcss-normalize-unicode": "^4.0.1", "postcss-normalize-url": "^4.0.1", "postcss-normalize-whitespace": "^4.0.2", "postcss-ordered-values": "^4.1.2", "postcss-reduce-initial": "^4.0.3", "postcss-reduce-transforms": "^4.0.2", "postcss-svgo": "^4.0.2", "postcss-unique-selectors": "^4.0.1"}}, "cssnano-util-get-arguments": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz", "integrity": "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8="}, "cssnano-util-get-match": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz", "integrity": "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0="}, "cssnano-util-raw-cache": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz", "integrity": "sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA==", "requires": {"postcss": "^7.0.0"}}, "cssnano-util-same-parent": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz", "integrity": "sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q=="}, "csso": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/csso/-/csso-4.0.3.tgz", "integrity": "sha1-DZmF3IUsfMKyys+74QeQFNGo6QM=", "requires": {"css-tree": "1.0.0-alpha.39"}, "dependencies": {"css-tree": {"version": "1.0.0-alpha.39", "resolved": "http://registry.npm.baidu-int.com/css-tree/-/css-tree-1.0.0-alpha.39.tgz", "integrity": "sha1-K/8//huz93bPfu/ZHuXLp3oUnus=", "requires": {"mdn-data": "2.0.6", "source-map": "^0.6.1"}}, "mdn-data": {"version": "2.0.6", "resolved": "http://registry.npm.baidu-int.com/mdn-data/-/mdn-data-2.0.6.tgz", "integrity": "sha1-hS3GD8ql2qLoz2yRicRA7T4EKXg="}, "source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "cyclist": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/cyclist/-/cyclist-1.0.1.tgz", "integrity": "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk="}, "dashdash": {"version": "1.14.1", "resolved": "http://registry.npm.baidu-int.com/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "optional": true, "requires": {"assert-plus": "^1.0.0"}}, "debug": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-4.1.1.tgz", "integrity": "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=", "requires": {"ms": "^2.1.1"}}, "decamelize": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="}, "decode-uri-component": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="}, "decompress": {"version": "4.2.1", "resolved": "http://registry.npm.baidu-int.com/decompress/-/decompress-4.2.1.tgz", "integrity": "sha1-AH9VzGpiwFWvo3wH62pO4bdz8Rg=", "requires": {"decompress-tar": "^4.0.0", "decompress-tarbz2": "^4.0.0", "decompress-targz": "^4.0.0", "decompress-unzip": "^4.0.1", "graceful-fs": "^4.1.10", "make-dir": "^1.0.0", "pify": "^2.3.0", "strip-dirs": "^2.0.0"}, "dependencies": {"make-dir": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/make-dir/-/make-dir-1.3.0.tgz", "integrity": "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==", "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "pify": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="}}}, "decompress-response": {"version": "3.3.0", "resolved": "http://registry.npm.baidu-int.com/decompress-response/-/decompress-response-3.3.0.tgz", "integrity": "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=", "requires": {"mimic-response": "^1.0.0"}}, "decompress-tar": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/decompress-tar/-/decompress-tar-4.1.1.tgz", "integrity": "sha1-cYy9P8sWIJcW5womuE57pFkuWvE=", "requires": {"file-type": "^5.2.0", "is-stream": "^1.1.0", "tar-stream": "^1.5.2"}, "dependencies": {"file-type": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/file-type/-/file-type-5.2.0.tgz", "integrity": "sha1-LdvqfHP/42No365J3DOMBYwritY="}}}, "decompress-tarbz2": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/decompress-tarbz2/-/decompress-tarbz2-4.1.1.tgz", "integrity": "sha1-MIKluIDqQEOBY0nzeLVsUWvho5s=", "requires": {"decompress-tar": "^4.1.0", "file-type": "^6.1.0", "is-stream": "^1.1.0", "seek-bzip": "^1.0.5", "unbzip2-stream": "^1.0.9"}, "dependencies": {"file-type": {"version": "6.2.0", "resolved": "http://registry.npm.baidu-int.com/file-type/-/file-type-6.2.0.tgz", "integrity": "sha1-5QzXXTVv/tTjBtxPW89Sp5kDqRk="}}}, "decompress-targz": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/decompress-targz/-/decompress-targz-4.1.1.tgz", "integrity": "sha1-wJvDXE0R894J8tLaU+neI+fOHu4=", "requires": {"decompress-tar": "^4.1.1", "file-type": "^5.2.0", "is-stream": "^1.1.0"}, "dependencies": {"file-type": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/file-type/-/file-type-5.2.0.tgz", "integrity": "sha1-LdvqfHP/42No365J3DOMBYwritY="}}}, "decompress-unzip": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/decompress-unzip/-/decompress-unzip-4.0.1.tgz", "integrity": "sha1-3qrM39FK6vhVePczroIQ+bSEj2k=", "requires": {"file-type": "^3.8.0", "get-stream": "^2.2.0", "pify": "^2.3.0", "yauzl": "^2.4.2"}, "dependencies": {"file-type": {"version": "3.9.0", "resolved": "http://registry.npm.baidu-int.com/file-type/-/file-type-3.9.0.tgz", "integrity": "sha1-JXoHg4TR24CHvESdEH1SpSZyuek="}, "get-stream": {"version": "2.3.1", "resolved": "http://registry.npm.baidu-int.com/get-stream/-/get-stream-2.3.1.tgz", "integrity": "sha1-Xzj5PzRgCWZu4BUKBUFn+Rvdld4=", "requires": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}}, "pify": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="}}}, "deep-equal": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/deep-equal/-/deep-equal-1.1.1.tgz", "integrity": "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==", "requires": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}}, "deep-is": {"version": "0.1.3", "resolved": "http://registry.npm.baidu-int.com/deep-is/-/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=", "dev": true}, "default-gateway": {"version": "4.2.0", "resolved": "http://registry.npm.baidu-int.com/default-gateway/-/default-gateway-4.2.0.tgz", "integrity": "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=", "requires": {"execa": "^1.0.0", "ip-regex": "^2.1.0"}}, "define-properties": {"version": "1.1.3", "resolved": "http://registry.npm.baidu-int.com/define-properties/-/define-properties-1.1.3.tgz", "integrity": "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==", "requires": {"object-keys": "^1.0.12"}}, "define-property": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "del": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/del/-/del-4.1.1.tgz", "integrity": "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=", "requires": {"@types/glob": "^7.1.1", "globby": "^6.1.0", "is-path-cwd": "^2.0.0", "is-path-in-cwd": "^2.0.0", "p-map": "^2.0.0", "pify": "^4.0.1", "rimraf": "^2.6.3"}, "dependencies": {"globby": {"version": "6.1.0", "resolved": "http://registry.npm.baidu-int.com/globby/-/globby-6.1.0.tgz", "integrity": "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=", "requires": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "dependencies": {"pify": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="}}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "optional": true}, "depd": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "dependency-cruiser": {"version": "9.9.2", "resolved": "http://registry.npm.baidu-int.com/dependency-cruiser/-/dependency-cruiser-9.9.2.tgz", "integrity": "sha1-YcfyPtSxyUd5NbctPj94ETNgMq0=", "requires": {"acorn": "7.3.1", "acorn-loose": "7.1.0", "acorn-walk": "7.2.0", "ajv": "6.12.3", "chalk": "4.1.0", "commander": "5.1.0", "enhanced-resolve": "4.2.0", "figures": "3.2.0", "get-stream": "5.1.0", "glob": "7.1.6", "handlebars": "4.7.6", "indent-string": "4.0.0", "inquirer": "7.3.1", "json5": "2.1.3", "lodash": "4.17.19", "pnp-webpack-plugin": "1.6.4", "safe-regex": "2.1.1", "semver": "7.3.2", "semver-try-require": "3.0.0", "teamcity-service-messages": "0.1.11", "tsconfig-paths-webpack-plugin": "3.2.0", "wrap-ansi": "7.0.0"}, "dependencies": {"ajv": {"version": "6.12.3", "resolved": "http://registry.npm.baidu-int.com/ajv/-/ajv-6.12.3.tgz", "integrity": "sha1-GMWvOKER3etPJpe9eNaKvByr1wY=", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-escapes": {"version": "4.3.1", "resolved": "http://registry.npm.baidu-int.com/ansi-escapes/-/ansi-escapes-4.3.1.tgz", "integrity": "sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=", "requires": {"type-fest": "^0.11.0"}}, "ansi-regex": {"version": "5.0.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-5.0.0.tgz", "integrity": "sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg=="}, "ansi-styles": {"version": "4.3.0", "resolved": "http://registry.npm.baidu-int.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/chalk/-/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://registry.npm.baidu-int.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "commander": {"version": "5.1.0", "resolved": "http://registry.npm.baidu-int.com/commander/-/commander-5.1.0.tgz", "integrity": "sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4="}, "emoji-regex": {"version": "8.0.0", "resolved": "http://registry.npm.baidu-int.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "get-stream": {"version": "5.1.0", "resolved": "http://registry.npm.baidu-int.com/get-stream/-/get-stream-5.1.0.tgz", "integrity": "sha1-ASA83JJZf5uQkGfD5lbMH008Tck=", "requires": {"pump": "^3.0.0"}}, "has-flag": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="}, "inquirer": {"version": "7.3.1", "resolved": "http://registry.npm.baidu-int.com/inquirer/-/inquirer-7.3.1.tgz", "integrity": "sha1-rGq6Gr391a005waTcEEe26F/ZDk=", "requires": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.16", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.6.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="}, "lodash": {"version": "4.17.19", "resolved": "http://registry.npm.baidu-int.com/lodash/-/lodash-4.17.19.tgz", "integrity": "sha1-5I3e2+MLMyF4PFtDAfvTU7weSks="}, "safe-regex": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/safe-regex/-/safe-regex-2.1.1.tgz", "integrity": "sha512-rx+x8AMzKb5Q5lQ95Zoi6ZbJqwCLkqi3XuJXp5P3rT8OEc6sZCJG5AE5dU3lsgRr/F4Bs31jSlVN+j5KrsGu9A==", "requires": {"regexp-tree": "~0.1.1"}}, "semver": {"version": "7.3.2", "resolved": "http://registry.npm.baidu-int.com/semver/-/semver-7.3.2.tgz", "integrity": "sha1-YElisFK4HtB4aq6EOJ/7pw/9OTg="}, "string-width": {"version": "4.2.0", "resolved": "http://registry.npm.baidu-int.com/string-width/-/string-width-4.2.0.tgz", "integrity": "sha512-zUz5JD+tgqtuDjMhwIg5uFVV3dtqZ9yQJlZVfq4I01/K5Paj5UHj7VyrQOJvzawSVlKpObApbfD0Ed6yJc+1eg==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.0"}}, "strip-ansi": {"version": "6.0.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-6.0.0.tgz", "integrity": "sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==", "requires": {"ansi-regex": "^5.0.0"}}, "supports-color": {"version": "7.2.0", "resolved": "http://registry.npm.baidu-int.com/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "http://registry.npm.baidu-int.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}}}, "des.js": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/des.js/-/des.js-1.0.1.tgz", "integrity": "sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA==", "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "destroy": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "detect-file": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc="}, "detect-node": {"version": "2.0.4", "resolved": "http://registry.npm.baidu-int.com/detect-node/-/detect-node-2.0.4.tgz", "integrity": "sha512-ZIzRpLJrOj7jjP2miAtgqIfmzbxa4ZOr5jJc601zklsfEx9oTzmmj2nVpIPRpNlRTIh8lc1kyViIY7BWSGNmKw=="}, "diffie-hellman": {"version": "5.0.3", "resolved": "http://registry.npm.baidu-int.com/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==", "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "dir-glob": {"version": "2.2.2", "resolved": "http://registry.npm.baidu-int.com/dir-glob/-/dir-glob-2.2.2.tgz", "integrity": "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=", "requires": {"path-type": "^3.0.0"}}, "dns-equal": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="}, "dns-packet": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/dns-packet/-/dns-packet-1.3.1.tgz", "integrity": "sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=", "requires": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "dns-txt": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/dns-txt/-/dns-txt-2.0.2.tgz", "integrity": "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=", "requires": {"buffer-indexof": "^1.0.0"}}, "doctrine": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "requires": {"esutils": "^2.0.2"}}, "dom-converter": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/dom-converter/-/dom-converter-0.2.0.tgz", "integrity": "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==", "requires": {"utila": "~0.4"}}, "dom-serializer": {"version": "0.2.2", "resolved": "http://registry.npm.baidu-int.com/dom-serializer/-/dom-serializer-0.2.2.tgz", "integrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==", "requires": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "dependencies": {"domelementtype": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/domelementtype/-/domelementtype-2.0.1.tgz", "integrity": "sha512-5HOHUDsYZWV8FGWN0Njbr/Rn7f/eWSQi1v7+HsUVwXgn8nWWlL64zKDkS0n8ZmQ3mlWOMuXOnR+7Nx/5tMO5AQ=="}}}, "domain-browser": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto="}, "domelementtype": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/domelementtype/-/domelementtype-1.3.1.tgz", "integrity": "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="}, "domhandler": {"version": "2.4.2", "resolved": "http://registry.npm.baidu-int.com/domhandler/-/domhandler-2.4.2.tgz", "integrity": "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==", "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.7.0", "resolved": "http://registry.npm.baidu-int.com/domutils/-/domutils-1.7.0.tgz", "integrity": "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=", "requires": {"dom-serializer": "0", "domelementtype": "1"}}, "dot-case": {"version": "3.0.3", "resolved": "http://registry.npm.baidu-int.com/dot-case/-/dot-case-3.0.3.tgz", "integrity": "sha1-IdO1Lvqroupf2odbsaqBJFIc9Ko=", "requires": {"no-case": "^3.0.3", "tslib": "^1.10.0"}}, "dot-prop": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/dot-prop/-/dot-prop-5.2.0.tgz", "integrity": "sha512-uEUyaDKoSQ1M4Oq8l45hSE26SnTxL6snNnqvK/VWx5wJhmff5z0FUVJDKDanor/6w3kzE3i7XZOk+7wC0EXr1A==", "requires": {"is-obj": "^2.0.0"}}, "download": {"version": "7.1.0", "resolved": "http://registry.npm.baidu-int.com/download/-/download-7.1.0.tgz", "integrity": "sha512-xqnBTVd/E+GxJVrX5/eUJiLYjCGPwMpdL+jGhGU57BvtcA7wwhtHVbXBeUk51kOpW3S7Jn3BQbN9Q1R1Km2qDQ==", "requires": {"archive-type": "^4.0.0", "caw": "^2.0.1", "content-disposition": "^0.5.2", "decompress": "^4.2.0", "ext-name": "^5.0.0", "file-type": "^8.1.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^8.3.1", "make-dir": "^1.2.0", "p-event": "^2.1.0", "pify": "^3.0.0"}, "dependencies": {"make-dir": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/make-dir/-/make-dir-1.3.0.tgz", "integrity": "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==", "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "duplexer2": {"version": "0.1.4", "resolved": "http://registry.npm.baidu-int.com/duplexer2/-/duplexer2-0.1.4.tgz", "integrity": "sha1-ixLauHjA1p4+eJEFFmKjL8a93ME=", "requires": {"readable-stream": "^2.0.2"}}, "duplexer3": {"version": "0.1.4", "resolved": "http://registry.npm.baidu-int.com/duplexer3/-/duplexer3-0.1.4.tgz", "integrity": "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI="}, "duplexify": {"version": "3.7.1", "resolved": "http://registry.npm.baidu-int.com/duplexify/-/duplexify-3.7.1.tgz", "integrity": "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=", "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://registry.npm.baidu-int.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "optional": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "ee-first": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "electron-to-chromium": {"version": "1.3.485", "resolved": "http://registry.npm.baidu-int.com/electron-to-chromium/-/electron-to-chromium-1.3.485.tgz", "integrity": "sha1-7me6RPvg3dyd1DmvUy5ZP2yLqqY="}, "elliptic": {"version": "6.5.3", "resolved": "http://registry.npm.baidu-int.com/elliptic/-/elliptic-6.5.3.tgz", "integrity": "sha1-y1nrLv2vc6C9eMzXAVpirW4Pk9Y=", "requires": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "emoji-regex": {"version": "7.0.3", "resolved": "http://registry.npm.baidu-int.com/emoji-regex/-/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="}, "emojis-list": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/emojis-list/-/emojis-list-3.0.0.tgz", "integrity": "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="}, "encodeurl": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "encoding": {"version": "0.1.13", "resolved": "http://registry.npm.baidu-int.com/encoding/-/encoding-0.1.13.tgz", "integrity": "sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=", "requires": {"iconv-lite": "^0.6.2"}, "dependencies": {"iconv-lite": {"version": "0.6.2", "resolved": "http://registry.npm.baidu-int.com/iconv-lite/-/iconv-lite-0.6.2.tgz", "integrity": "sha1-zhPRh1sMOmdL1qBLf3awGxtt7QE=", "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}}}, "end-of-stream": {"version": "1.4.4", "resolved": "http://registry.npm.baidu-int.com/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "4.2.0", "resolved": "http://registry.npm.baidu-int.com/enhanced-resolve/-/enhanced-resolve-4.2.0.tgz", "integrity": "sha1-XUO9pKD9RHyw675xvvje/4gFrQ0=", "requires": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "dependencies": {"memory-fs": {"version": "0.5.0", "resolved": "http://registry.npm.baidu-int.com/memory-fs/-/memory-fs-0.5.0.tgz", "integrity": "sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==", "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}}}, "entities": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/entities/-/entities-2.0.3.tgz", "integrity": "sha1-XEh+V0Krk8Fau12iJ1m4WQ7AO38="}, "errno": {"version": "0.1.7", "resolved": "http://registry.npm.baidu-int.com/errno/-/errno-0.1.7.tgz", "integrity": "sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=", "requires": {"prr": "~1.0.1"}}, "error-ex": {"version": "1.3.2", "resolved": "http://registry.npm.baidu-int.com/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "requires": {"is-arrayish": "^0.2.1"}}, "es-abstract": {"version": "1.17.6", "resolved": "http://registry.npm.baidu-int.com/es-abstract/-/es-abstract-1.17.6.tgz", "integrity": "sha1-kUIHFweFeyysx7iey2cDFsPi1So=", "requires": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.0", "is-regex": "^1.1.0", "object-inspect": "^1.7.0", "object-keys": "^1.1.1", "object.assign": "^4.1.0", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}}, "es-to-primitive": {"version": "1.2.1", "resolved": "http://registry.npm.baidu-int.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "integrity": "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==", "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escalade": {"version": "3.0.1", "resolved": "http://registry.npm.baidu-int.com/escalade/-/escalade-3.0.1.tgz", "integrity": "sha1-UlaKd0Q/aSfNCrnHMSkTdTPJZe0="}, "escape-html": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "eslint": {"version": "5.16.0", "resolved": "http://registry.npm.baidu-int.com/eslint/-/eslint-5.16.0.tgz", "integrity": "sha1-oeOsGq5KP72Clvz496tzFMu2q+o=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "ajv": "^6.9.1", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^4.0.3", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.1", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^6.2.2", "js-yaml": "^3.13.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.11", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^5.2.3", "text-table": "^0.2.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "cli-cursor": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/cli-cursor/-/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "requires": {"restore-cursor": "^2.0.0"}}, "cli-width": {"version": "2.2.1", "resolved": "http://registry.npm.baidu-int.com/cli-width/-/cli-width-2.2.1.tgz", "integrity": "sha1-sEM9C06chH7xiGik7xb9X8gnHEg=", "dev": true}, "figures": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/figures/-/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "import-fresh": {"version": "3.2.1", "resolved": "http://registry.npm.baidu-int.com/import-fresh/-/import-fresh-3.2.1.tgz", "integrity": "sha512-6e1q1cnWP2RXD9/keSkxHScg508CdXqXWgWBaETNhyuBFz+kUZlKboh+ISK+bU++DmbHimVBrOz/zzPe0sZ3sQ==", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "inquirer": {"version": "6.5.2", "resolved": "http://registry.npm.baidu-int.com/inquirer/-/inquirer-6.5.2.tgz", "integrity": "sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ==", "dev": true, "requires": {"ansi-escapes": "^3.2.0", "chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.3", "figures": "^2.0.0", "lodash": "^4.17.12", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.4.0", "string-width": "^2.1.0", "strip-ansi": "^5.1.0", "through": "^2.3.6"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=", "dev": true}, "strip-ansi": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}}}, "mimic-fn": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/mimic-fn/-/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "dev": true}, "mute-stream": {"version": "0.0.7", "resolved": "http://registry.npm.baidu-int.com/mute-stream/-/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "onetime": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/onetime/-/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "resolve-from": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true}, "restore-cursor": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/restore-cursor/-/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "string-width": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/string-width/-/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "eslint-config-airbnb-base": {"version": "13.2.0", "resolved": "http://registry.npm.baidu-int.com/eslint-config-airbnb-base/-/eslint-config-airbnb-base-13.2.0.tgz", "integrity": "sha512-1mg/7eoB4AUeB0X1c/ho4vb2gYkNH8Trr/EgCT/aGmKhhG+F6vF5s8+iRBlWAzFIAphxIdp3YfEKgEl0f9Xg+w==", "dev": true, "requires": {"confusing-browser-globals": "^1.0.5", "object.assign": "^4.1.0", "object.entries": "^1.1.0"}}, "eslint-formatter-pretty": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/eslint-formatter-pretty/-/eslint-formatter-pretty-2.1.1.tgz", "integrity": "sha1-B5ShAJGV0U5EgFP+mWZ0E7fQLkQ=", "dev": true, "requires": {"ansi-escapes": "^3.1.0", "chalk": "^2.1.0", "eslint-rule-docs": "^1.1.5", "log-symbols": "^2.0.0", "plur": "^3.0.1", "string-width": "^2.0.0", "supports-hyperlinks": "^1.0.1"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "log-symbols": {"version": "2.2.0", "resolved": "http://registry.npm.baidu-int.com/log-symbols/-/log-symbols-2.2.0.tgz", "integrity": "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=", "dev": true, "requires": {"chalk": "^2.0.1"}}, "string-width": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/string-width/-/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "eslint-import-resolver-node": {"version": "0.3.4", "resolved": "http://registry.npm.baidu-int.com/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.4.tgz", "integrity": "sha1-hf+oGULCUBLYIxCW3fZ5wDBCxxc=", "dev": true, "requires": {"debug": "^2.6.9", "resolve": "^1.13.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-module-utils": {"version": "2.6.0", "resolved": "http://registry.npm.baidu-int.com/eslint-module-utils/-/eslint-module-utils-2.6.0.tgz", "integrity": "sha1-V569CU9Wr3eX0ZyYZsnJSGYpv6Y=", "dev": true, "requires": {"debug": "^2.6.9", "pkg-dir": "^2.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "find-up": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/find-up/-/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "p-limit": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/p-limit/-/p-limit-1.3.0.tgz", "integrity": "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/p-locate/-/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/p-try/-/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}, "pkg-dir": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/pkg-dir/-/pkg-dir-2.0.0.tgz", "integrity": "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=", "dev": true, "requires": {"find-up": "^2.1.0"}}}}, "eslint-plugin-import": {"version": "2.22.0", "resolved": "http://registry.npm.baidu-int.com/eslint-plugin-import/-/eslint-plugin-import-2.22.0.tgz", "integrity": "sha1-kvdzb+H94+Led2I8g43Zkv9f+34=", "dev": true, "requires": {"array-includes": "^3.1.1", "array.prototype.flat": "^1.2.3", "contains-path": "^0.1.0", "debug": "^2.6.9", "doctrine": "1.5.0", "eslint-import-resolver-node": "^0.3.3", "eslint-module-utils": "^2.6.0", "has": "^1.0.3", "minimatch": "^3.0.4", "object.values": "^1.1.1", "read-pkg-up": "^2.0.0", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "doctrine": {"version": "1.5.0", "resolved": "http://registry.npm.baidu-int.com/doctrine/-/doctrine-1.5.0.tgz", "integrity": "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=", "dev": true, "requires": {"esutils": "^2.0.2", "isarray": "^1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-rule-docs": {"version": "1.1.197", "resolved": "http://registry.npm.baidu-int.com/eslint-rule-docs/-/eslint-rule-docs-1.1.197.tgz", "integrity": "sha1-Ibo5GOnmLd07Ng7+uM5Ijz2ddEo=", "dev": true}, "eslint-scope": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/eslint-scope/-/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "1.4.3", "resolved": "http://registry.npm.baidu-int.com/eslint-utils/-/eslint-utils-1.4.3.tgz", "integrity": "sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==", "dev": true, "requires": {"eslint-visitor-keys": "^1.1.0"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true}, "espree": {"version": "5.0.1", "resolved": "http://registry.npm.baidu-int.com/espree/-/espree-5.0.1.tgz", "integrity": "sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=", "dev": true, "requires": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "dependencies": {"acorn": {"version": "6.4.1", "resolved": "http://registry.npm.baidu-int.com/acorn/-/acorn-6.4.1.tgz", "integrity": "sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ=", "dev": true}}}, "esprima": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}, "esquery": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/esquery/-/esquery-1.3.1.tgz", "integrity": "sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=", "dev": true, "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.1.0", "resolved": "http://registry.npm.baidu-int.com/estraverse/-/estraverse-5.1.0.tgz", "integrity": "sha1-N0MJ05/ZNa5QDnuS6Ka0xyDllkI=", "dev": true}}}, "esrecurse": {"version": "4.2.1", "resolved": "http://registry.npm.baidu-int.com/esrecurse/-/esrecurse-4.2.1.tgz", "integrity": "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=", "requires": {"estraverse": "^4.1.0"}}, "estraverse": {"version": "4.3.0", "resolved": "http://registry.npm.baidu-int.com/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="}, "esutils": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="}, "etag": {"version": "1.8.1", "resolved": "http://registry.npm.baidu-int.com/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "4.0.4", "resolved": "http://registry.npm.baidu-int.com/eventemitter3/-/eventemitter3-4.0.4.tgz", "integrity": "sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q="}, "events": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/events/-/events-3.1.0.tgz", "integrity": "sha1-hCea8bNMt1qoi/X/KR9tC9mzGlk="}, "eventsource": {"version": "1.0.7", "resolved": "http://registry.npm.baidu-int.com/eventsource/-/eventsource-1.0.7.tgz", "integrity": "sha512-4Ln17+vVT0k8aWq+t/bF5arcS3EpT9gYtW66EPacdj/mAFevznsnyoHLPy2BA8gbIQeIHoPsvwmfBftfcG//BQ==", "requires": {"original": "^1.0.0"}}, "evp_bytestokey": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "execa": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/execa/-/execa-1.0.0.tgz", "integrity": "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==", "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"get-stream": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/get-stream/-/get-stream-4.1.0.tgz", "integrity": "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==", "requires": {"pump": "^3.0.0"}}}}, "expand-brackets": {"version": "2.1.4", "resolved": "http://registry.npm.baidu-int.com/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "expand-tilde": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "requires": {"homedir-polyfill": "^1.0.1"}}, "express": {"version": "4.17.1", "resolved": "http://registry.npm.baidu-int.com/express/-/express-4.17.1.tgz", "integrity": "sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==", "requires": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"array-flatten": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "path-to-regexp": {"version": "0.1.7", "resolved": "http://registry.npm.baidu-int.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "qs": {"version": "6.7.0", "resolved": "http://registry.npm.baidu-int.com/qs/-/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="}}}, "ext-list": {"version": "2.2.2", "resolved": "http://registry.npm.baidu-int.com/ext-list/-/ext-list-2.2.2.tgz", "integrity": "sha1-C5jmTtgvWs8PKTG6v2khLvUt3Tc=", "requires": {"mime-db": "^1.28.0"}}, "ext-name": {"version": "5.0.0", "resolved": "http://registry.npm.baidu-int.com/ext-name/-/ext-name-5.0.0.tgz", "integrity": "sha1-cHgZgdGD7hXROZPIgiBFxQbI8KY=", "requires": {"ext-list": "^2.0.0", "sort-keys-length": "^1.0.0"}}, "extend": {"version": "3.0.2", "resolved": "http://registry.npm.baidu-int.com/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "optional": true}, "extend-shallow": {"version": "3.0.2", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "requires": {"is-plain-object": "^2.0.4"}}}}, "external-editor": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/external-editor/-/external-editor-3.1.0.tgz", "integrity": "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==", "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "extglob": {"version": "2.0.4", "resolved": "http://registry.npm.baidu-int.com/extglob/-/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "optional": true}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://registry.npm.baidu-int.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="}, "fast-glob": {"version": "2.2.7", "resolved": "http://registry.npm.baidu-int.com/fast-glob/-/fast-glob-2.2.7.tgz", "integrity": "sha1-aVOFfDr6R1//ku5gFdUtpwpM050=", "requires": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://registry.npm.baidu-int.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "faye-websocket": {"version": "0.10.0", "resolved": "http://registry.npm.baidu-int.com/faye-websocket/-/faye-websocket-0.10.0.tgz", "integrity": "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=", "requires": {"websocket-driver": ">=0.5.1"}}, "fd-slicer": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/fd-slicer/-/fd-slicer-1.1.0.tgz", "integrity": "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=", "requires": {"pend": "~1.2.0"}}, "figgy-pudding": {"version": "3.5.2", "resolved": "http://registry.npm.baidu-int.com/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "integrity": "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4="}, "figures": {"version": "3.2.0", "resolved": "http://registry.npm.baidu-int.com/figures/-/figures-3.2.0.tgz", "integrity": "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=", "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "5.0.1", "resolved": "http://registry.npm.baidu-int.com/file-entry-cache/-/file-entry-cache-5.0.1.tgz", "integrity": "sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=", "dev": true, "requires": {"flat-cache": "^2.0.1"}}, "file-type": {"version": "8.1.0", "resolved": "http://registry.npm.baidu-int.com/file-type/-/file-type-8.1.0.tgz", "integrity": "sha512-qyQ0pzAy78gVoJsmYeNgl8uH8yKhr1lVhW7JbzJmnlRi0I4R2eEDEJZVKG8agpDnLpacwNbDhLNG/LMdxHD2YQ=="}, "file-uri-to-path": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "optional": true}, "filename-reserved-regex": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/filename-reserved-regex/-/filename-reserved-regex-2.0.0.tgz", "integrity": "sha1-q/c9+rc10EVECr/qLZHzieu/oik="}, "filenamify": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/filenamify/-/filenamify-2.1.0.tgz", "integrity": "sha512-ICw7NTT6RsDp2rnYKVd8Fu4cr6ITzGy3+u4vUujPkabyaz+03F24NWEX7fs5fp+kBonlaqPH8fAO2NM+SXt/JA==", "requires": {"filename-reserved-regex": "^2.0.0", "strip-outer": "^1.0.0", "trim-repeated": "^1.0.0"}}, "fill-range": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "finalhandler": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "find-cache-dir": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "requires": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}}, "find-up": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "requires": {"locate-path": "^3.0.0"}}, "findup-sync": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/findup-sync/-/findup-sync-3.0.0.tgz", "integrity": "sha1-F7EI+e5RLft6XH88iyfqnhqcCNE=", "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "flat-cache": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/flat-cache/-/flat-cache-2.0.1.tgz", "integrity": "sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=", "dev": true, "requires": {"flatted": "^2.0.0", "rimraf": "2.6.3", "write": "1.0.3"}, "dependencies": {"rimraf": {"version": "2.6.3", "resolved": "http://registry.npm.baidu-int.com/rimraf/-/rimraf-2.6.3.tgz", "integrity": "sha1-stEE/g2Psnz54KHNqCYt04M8bKs=", "dev": true, "requires": {"glob": "^7.1.3"}}}}, "flatted": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/flatted/-/flatted-2.0.2.tgz", "integrity": "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=", "dev": true}, "flush-write-stream": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "integrity": "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=", "requires": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "follow-redirects": {"version": "1.12.1", "resolved": "http://registry.npm.baidu-int.com/follow-redirects/-/follow-redirects-1.12.1.tgz", "integrity": "sha1-3lSmIFMRuT1gOY68Ac9wFWgjErY="}, "for-in": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="}, "forever-agent": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "optional": true}, "forwarded": {"version": "0.1.2", "resolved": "http://registry.npm.baidu-int.com/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="}, "fragment-cache": {"version": "0.2.1", "resolved": "http://registry.npm.baidu-int.com/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "requires": {"map-cache": "^0.2.2"}}, "fresh": {"version": "0.5.2", "resolved": "http://registry.npm.baidu-int.com/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "from2": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "fs-constants": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="}, "fs-walk": {"version": "0.0.2", "resolved": "http://registry.npm.baidu-int.com/fs-walk/-/fs-walk-0.0.2.tgz", "integrity": "sha1-IhA4W9vDKrUZM87lAu8YUWeXYE0=", "requires": {"async": "*"}}, "fs-write-stream-atomic": {"version": "1.0.10", "resolved": "http://registry.npm.baidu-int.com/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "2.1.3", "resolved": "http://registry.npm.baidu-int.com/fsevents/-/fsevents-2.1.3.tgz", "integrity": "sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4=", "optional": true}, "fstream": {"version": "1.0.12", "resolved": "http://registry.npm.baidu-int.com/fstream/-/fstream-1.0.12.tgz", "integrity": "sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=", "requires": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}}, "function-bind": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "gensync": {"version": "1.0.0-beta.1", "resolved": "http://registry.npm.baidu-int.com/gensync/-/gensync-1.0.0-beta.1.tgz", "integrity": "sha1-WPQ2H/mH5f9uHnohCCeqNx6qwmk="}, "get-caller-file": {"version": "2.0.5", "resolved": "http://registry.npm.baidu-int.com/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="}, "get-proxy": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/get-proxy/-/get-proxy-2.1.0.tgz", "integrity": "sha1-NJ8rTZHUTE1NTpy6KtkBQ/rF75M=", "requires": {"npm-conf": "^1.1.0"}}, "get-stream": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/get-stream/-/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="}, "get-value": {"version": "2.0.6", "resolved": "http://registry.npm.baidu-int.com/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="}, "getpass": {"version": "0.1.7", "resolved": "http://registry.npm.baidu-int.com/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "optional": true, "requires": {"assert-plus": "^1.0.0"}}, "gettext-parser": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/gettext-parser/-/gettext-parser-1.4.0.tgz", "integrity": "sha512-sedZYLHlHeBop/gZ1jdg59hlUEcpcZJofLq2JFwJT1zTqAU3l2wFv6IsuwFHGqbiT9DWzMUW4/em2+hspnmMMA==", "requires": {"encoding": "^0.1.12", "safe-buffer": "^5.1.1"}}, "glob": {"version": "7.1.6", "resolved": "http://registry.npm.baidu-int.com/glob/-/glob-7.1.6.tgz", "integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}}}, "glob-to-regexp": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz", "integrity": "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs="}, "global-modules": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/global-modules/-/global-modules-2.0.0.tgz", "integrity": "sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=", "requires": {"global-prefix": "^3.0.0"}, "dependencies": {"global-prefix": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/global-prefix/-/global-prefix-3.0.0.tgz", "integrity": "sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=", "requires": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}}}}, "global-prefix": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=", "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}}, "globals": {"version": "11.12.0", "resolved": "http://registry.npm.baidu-int.com/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="}, "globby": {"version": "9.2.0", "resolved": "http://registry.npm.baidu-int.com/globby/-/globby-9.2.0.tgz", "integrity": "sha1-/QKacGxwPSm90XD0tts6P3p8tj0=", "requires": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.2", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}}, "got": {"version": "8.3.2", "resolved": "http://registry.npm.baidu-int.com/got/-/got-8.3.2.tgz", "integrity": "sha512-qjUJ5U/hawxosMryILofZCkm3C84PLJS/0grRIpjAwu+Lkxxj5cxeCU25BG0/3mDSpXKTyZr8oh8wIgLaH0QCw==", "requires": {"@sindresorhus/is": "^0.7.0", "cacheable-request": "^2.1.1", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "into-stream": "^3.1.0", "is-retry-allowed": "^1.1.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "mimic-response": "^1.0.0", "p-cancelable": "^0.4.0", "p-timeout": "^2.0.1", "pify": "^3.0.0", "safe-buffer": "^5.1.1", "timed-out": "^4.0.1", "url-parse-lax": "^3.0.0", "url-to-options": "^1.0.1"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "graceful-fs": {"version": "4.2.4", "resolved": "http://registry.npm.baidu-int.com/graceful-fs/-/graceful-fs-4.2.4.tgz", "integrity": "sha1-Ila94U02MpWMRl68ltxGfKB6Kfs="}, "graceful-readlink": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/graceful-readlink/-/graceful-readlink-1.0.1.tgz", "integrity": "sha1-TK+tdrxi8C+gObL5Tpo906ORpyU="}, "handle-thing": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/handle-thing/-/handle-thing-2.0.1.tgz", "integrity": "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="}, "handlebars": {"version": "4.7.6", "resolved": "http://registry.npm.baidu-int.com/handlebars/-/handlebars-4.7.6.tgz", "integrity": "sha1-1MBcG6+Q6ZRfd6pop6IZqkp9904=", "requires": {"minimist": "^1.2.5", "neo-async": "^2.6.0", "source-map": "^0.6.1", "uglify-js": "^3.1.4", "wordwrap": "^1.0.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "has": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "has-symbol-support-x": {"version": "1.4.2", "resolved": "http://registry.npm.baidu-int.com/has-symbol-support-x/-/has-symbol-support-x-1.4.2.tgz", "integrity": "sha1-FAn5i8ACR9pF2mfO4KNvKC/yZFU="}, "has-symbols": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/has-symbols/-/has-symbols-1.0.1.tgz", "integrity": "sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg="}, "has-to-string-tag-x": {"version": "1.4.1", "resolved": "http://registry.npm.baidu-int.com/has-to-string-tag-x/-/has-to-string-tag-x-1.4.1.tgz", "integrity": "sha1-oEWrOD17SyASoAFIqwql8pAETU0=", "requires": {"has-symbol-support-x": "^1.4.1"}}, "has-value": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/hash-base/-/hash-base-3.1.0.tgz", "integrity": "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=", "requires": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://registry.npm.baidu-int.com/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://registry.npm.baidu-int.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="}}}, "hash.js": {"version": "1.1.7", "resolved": "http://registry.npm.baidu-int.com/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=", "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "hawk": {"version": "3.1.3", "resolved": "http://registry.npm.baidu-int.com/hawk/-/hawk-3.1.3.tgz", "integrity": "sha1-B4REvXwWQLD+VA0sm3PVlnjo4cQ=", "optional": true, "requires": {"boom": "2.x.x", "cryptiles": "2.x.x", "hoek": "2.x.x", "sntp": "1.x.x"}}, "he": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/he/-/he-1.2.0.tgz", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="}, "hex-color-regex": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/hex-color-regex/-/hex-color-regex-1.1.0.tgz", "integrity": "sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ=="}, "hmac-drbg": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "hoek": {"version": "2.16.3", "resolved": "http://registry.npm.baidu-int.com/hoek/-/hoek-2.16.3.tgz", "integrity": "sha1-ILt0A9POo5jpHcRxCo/xuCdKJe0=", "optional": true}, "homedir-polyfill": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=", "requires": {"parse-passwd": "^1.0.0"}}, "hosted-git-info": {"version": "2.8.8", "resolved": "http://registry.npm.baidu-int.com/hosted-git-info/-/hosted-git-info-2.8.8.tgz", "integrity": "sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=", "dev": true}, "hpack.js": {"version": "2.1.6", "resolved": "http://registry.npm.baidu-int.com/hpack.js/-/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "requires": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "hsl-regex": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/hsl-regex/-/hsl-regex-1.0.0.tgz", "integrity": "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4="}, "hsla-regex": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/hsla-regex/-/hsla-regex-1.0.0.tgz", "integrity": "sha1-wc56MWjIxmFAM6S194d/OyJfnDg="}, "html-comment-regex": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/html-comment-regex/-/html-comment-regex-1.1.2.tgz", "integrity": "sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ=="}, "html-entities": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/html-entities/-/html-entities-1.3.1.tgz", "integrity": "sha1-+5oaS1sUxdq6gtPjTGrk/nAaDkQ="}, "html-minifier-terser": {"version": "5.1.1", "resolved": "http://registry.npm.baidu-int.com/html-minifier-terser/-/html-minifier-terser-5.1.1.tgz", "integrity": "sha1-ki6W8fO7YIMsJjS3mIQJY4mx8FQ=", "requires": {"camel-case": "^4.1.1", "clean-css": "^4.2.3", "commander": "^4.1.1", "he": "^1.2.0", "param-case": "^3.0.3", "relateurl": "^0.2.7", "terser": "^4.6.3"}, "dependencies": {"commander": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/commander/-/commander-4.1.1.tgz", "integrity": "sha1-n9YCvZNilOnp70aj9NaWQESxgGg="}}}, "html-webpack-plugin": {"version": "4.3.0", "resolved": "http://registry.npm.baidu-int.com/html-webpack-plugin/-/html-webpack-plugin-4.3.0.tgz", "integrity": "sha1-U7+PbWlsRjfVtlbT2YY9ic6BdP0=", "requires": {"@types/html-minifier-terser": "^5.0.0", "@types/tapable": "^1.0.5", "@types/webpack": "^4.41.8", "html-minifier-terser": "^5.0.1", "loader-utils": "^1.2.3", "lodash": "^4.17.15", "pretty-error": "^2.1.1", "tapable": "^1.1.3", "util.promisify": "1.0.0"}, "dependencies": {"util.promisify": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/util.promisify/-/util.promisify-1.0.0.tgz", "integrity": "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=", "requires": {"define-properties": "^1.1.2", "object.getownpropertydescriptors": "^2.0.3"}}}}, "htmlparser2": {"version": "3.10.1", "resolved": "http://registry.npm.baidu-int.com/htmlparser2/-/htmlparser2-3.10.1.tgz", "integrity": "sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=", "requires": {"domelementtype": "^1.3.1", "domhandler": "^2.3.0", "domutils": "^1.5.1", "entities": "^1.1.1", "inherits": "^2.0.1", "readable-stream": "^3.1.1"}, "dependencies": {"entities": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/entities/-/entities-1.1.2.tgz", "integrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w=="}, "readable-stream": {"version": "3.6.0", "resolved": "http://registry.npm.baidu-int.com/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}}}, "http-cache-semantics": {"version": "3.8.1", "resolved": "http://registry.npm.baidu-int.com/http-cache-semantics/-/http-cache-semantics-3.8.1.tgz", "integrity": "sha512-5ai2iksyV8ZXmnZhHH4rWPoxxistEexSi5936zIQ1bnNTW5VnA85B6P/VpXiRM017IgRvb2kKo1a//y+0wSp3w=="}, "http-deceiver": {"version": "1.2.7", "resolved": "http://registry.npm.baidu-int.com/http-deceiver/-/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="}, "http-errors": {"version": "1.7.2", "resolved": "http://registry.npm.baidu-int.com/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}}}, "http-proxy": {"version": "1.18.1", "resolved": "http://registry.npm.baidu-int.com/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "0.19.1", "resolved": "http://registry.npm.baidu-int.com/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz", "integrity": "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=", "requires": {"http-proxy": "^1.17.0", "is-glob": "^4.0.0", "lodash": "^4.17.11", "micromatch": "^3.1.10"}}, "https-browserify": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM="}, "iconv-lite": {"version": "0.4.24", "resolved": "http://registry.npm.baidu-int.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "icss-replace-symbols": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz", "integrity": "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0="}, "icss-utils": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/icss-utils/-/icss-utils-4.1.1.tgz", "integrity": "sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA==", "requires": {"postcss": "^7.0.14"}}, "ieee754": {"version": "1.1.13", "resolved": "http://registry.npm.baidu-int.com/ieee754/-/ieee754-1.1.13.tgz", "integrity": "sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q="}, "iferr": {"version": "0.1.5", "resolved": "http://registry.npm.baidu-int.com/iferr/-/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE="}, "ignore": {"version": "4.0.6", "resolved": "http://registry.npm.baidu-int.com/ignore/-/ignore-4.0.6.tgz", "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="}, "image-size": {"version": "0.5.5", "resolved": "http://registry.npm.baidu-int.com/image-size/-/image-size-0.5.5.tgz", "integrity": "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=", "optional": true}, "import-cwd": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/import-cwd/-/import-cwd-2.1.0.tgz", "integrity": "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=", "requires": {"import-from": "^2.1.0"}}, "import-fresh": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/import-fresh/-/import-fresh-2.0.0.tgz", "integrity": "sha1-2BNVwVYS04bGH53dOSLUMEgipUY=", "requires": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}}, "import-from": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/import-from/-/import-from-2.1.0.tgz", "integrity": "sha1-M1238qev/VOqpHHUuAId7ja387E=", "requires": {"resolve-from": "^3.0.0"}}, "import-local": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/import-local/-/import-local-2.0.0.tgz", "integrity": "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==", "requires": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://registry.npm.baidu-int.com/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o="}, "indent-string": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="}, "indexes-of": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/indexes-of/-/indexes-of-1.0.1.tgz", "integrity": "sha1-8w9xbI4r00bHtn0985FVZqfAVgc="}, "infer-owner": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A=="}, "inflight": {"version": "1.0.6", "resolved": "http://registry.npm.baidu-int.com/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://registry.npm.baidu-int.com/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "ini": {"version": "1.3.5", "resolved": "http://registry.npm.baidu-int.com/ini/-/ini-1.3.5.tgz", "integrity": "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc="}, "inquirer": {"version": "7.3.3", "resolved": "http://registry.npm.baidu-int.com/inquirer/-/inquirer-7.3.3.tgz", "integrity": "sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=", "requires": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.19", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.6.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}, "dependencies": {"ansi-escapes": {"version": "4.3.1", "resolved": "http://registry.npm.baidu-int.com/ansi-escapes/-/ansi-escapes-4.3.1.tgz", "integrity": "sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=", "requires": {"type-fest": "^0.11.0"}}, "ansi-regex": {"version": "5.0.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-5.0.0.tgz", "integrity": "sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg=="}, "ansi-styles": {"version": "4.3.0", "resolved": "http://registry.npm.baidu-int.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/chalk/-/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://registry.npm.baidu-int.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "emoji-regex": {"version": "8.0.0", "resolved": "http://registry.npm.baidu-int.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "has-flag": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="}, "lodash": {"version": "4.17.20", "resolved": "http://registry.npm.baidu-int.com/lodash/-/lodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI="}, "string-width": {"version": "4.2.0", "resolved": "http://registry.npm.baidu-int.com/string-width/-/string-width-4.2.0.tgz", "integrity": "sha512-zUz5JD+tgqtuDjMhwIg5uFVV3dtqZ9yQJlZVfq4I01/K5Paj5UHj7VyrQOJvzawSVlKpObApbfD0Ed6yJc+1eg==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.0"}}, "strip-ansi": {"version": "6.0.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-6.0.0.tgz", "integrity": "sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==", "requires": {"ansi-regex": "^5.0.0"}}, "supports-color": {"version": "7.2.0", "resolved": "http://registry.npm.baidu-int.com/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "internal-ip": {"version": "4.3.0", "resolved": "http://registry.npm.baidu-int.com/internal-ip/-/internal-ip-4.3.0.tgz", "integrity": "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=", "requires": {"default-gateway": "^4.2.0", "ipaddr.js": "^1.9.0"}}, "interpret": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/interpret/-/interpret-1.4.0.tgz", "integrity": "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4="}, "into-stream": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/into-stream/-/into-stream-3.1.0.tgz", "integrity": "sha1-lvsKk2wSur1v8XUqF9BWFqvQlMY=", "requires": {"from2": "^2.1.1", "p-is-promise": "^1.1.0"}}, "invariant": {"version": "2.2.4", "resolved": "http://registry.npm.baidu-int.com/invariant/-/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "requires": {"loose-envify": "^1.0.0"}}, "ip": {"version": "1.1.5", "resolved": "http://registry.npm.baidu-int.com/ip/-/ip-1.1.5.tgz", "integrity": "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="}, "ip-regex": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/ip-regex/-/ip-regex-2.1.0.tgz", "integrity": "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk="}, "ipaddr.js": {"version": "1.9.1", "resolved": "http://registry.npm.baidu-int.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="}, "irregular-plurals": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/irregular-plurals/-/irregular-plurals-2.0.0.tgz", "integrity": "sha512-Y75zBYLkh0lJ9qxeHlMjQ7bSbyiSqNW/UOPWDmzC7cXskL1hekSITh1Oc6JV0XCWWZ9DE8VYSB71xocLk3gmGw==", "dev": true}, "is-absolute-url": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/is-absolute-url/-/is-absolute-url-2.1.0.tgz", "integrity": "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY="}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://registry.npm.baidu-int.com/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-arguments": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/is-arguments/-/is-arguments-1.0.4.tgz", "integrity": "sha1-P6+WbHy6D/Q3+zH2JQCC/PBEjPM="}, "is-arrayish": {"version": "0.2.1", "resolved": "http://registry.npm.baidu-int.com/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="}, "is-binary-path": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "optional": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "http://registry.npm.baidu-int.com/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4="}, "is-callable": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/is-callable/-/is-callable-1.2.0.tgz", "integrity": "sha1-gzNlYLVKOONeOi33r9BFTWkUaLs="}, "is-color-stop": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/is-color-stop/-/is-color-stop-1.1.0.tgz", "integrity": "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=", "requires": {"css-color-names": "^0.0.4", "hex-color-regex": "^1.1.0", "hsl-regex": "^1.0.0", "hsla-regex": "^1.0.0", "rgb-regex": "^1.0.1", "rgba-regex": "^1.0.0"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://registry.npm.baidu-int.com/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/is-date-object/-/is-date-object-1.0.2.tgz", "integrity": "sha1-vac28s2P0G0yhE53Q7+nSUw7/X4="}, "is-descriptor": {"version": "0.1.6", "resolved": "http://registry.npm.baidu-int.com/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="}}}, "is-directory": {"version": "0.3.1", "resolved": "http://registry.npm.baidu-int.com/is-directory/-/is-directory-0.3.1.tgz", "integrity": "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="}, "is-extendable": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="}, "is-extglob": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "is-glob": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/is-glob/-/is-glob-4.0.1.tgz", "integrity": "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=", "requires": {"is-extglob": "^2.1.1"}}, "is-natural-number": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/is-natural-number/-/is-natural-number-4.0.1.tgz", "integrity": "sha1-q5124dtM7VHjXeDHLr7PCfc0zeg="}, "is-number": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-obj": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/is-obj/-/is-obj-2.0.0.tgz", "integrity": "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI="}, "is-object": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/is-object/-/is-object-1.0.1.tgz", "integrity": "sha1-iVJojF7C/9awPsyF52ngKQMINHA="}, "is-path-cwd": {"version": "2.2.0", "resolved": "http://registry.npm.baidu-int.com/is-path-cwd/-/is-path-cwd-2.2.0.tgz", "integrity": "sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ=="}, "is-path-in-cwd": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz", "integrity": "sha1-v+Lcomxp85cmWkAJljYCk1oFOss=", "requires": {"is-path-inside": "^2.1.0"}}, "is-path-inside": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/is-path-inside/-/is-path-inside-2.1.0.tgz", "integrity": "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=", "requires": {"path-is-inside": "^1.0.2"}}, "is-plain-obj": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "integrity": "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="}, "is-plain-object": {"version": "2.0.4", "resolved": "http://registry.npm.baidu-int.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "requires": {"isobject": "^3.0.1"}}, "is-regex": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/is-regex/-/is-regex-1.1.0.tgz", "integrity": "sha1-7OOOOJ5JDfDcIcrqK9WW+Yf3Z/8=", "requires": {"has-symbols": "^1.0.1"}}, "is-resolvable": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/is-resolvable/-/is-resolvable-1.1.0.tgz", "integrity": "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="}, "is-retry-allowed": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/is-retry-allowed/-/is-retry-allowed-1.2.0.tgz", "integrity": "sha512-RUbUeKwvm3XG2VYamhJL1xFktgjvPzL0Hq8C+6yrWIswDy3BIXGqCxhxkc30N9jqK311gVU137K8Ei55/zVJRg=="}, "is-stream": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="}, "is-string": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/is-string/-/is-string-1.0.5.tgz", "integrity": "sha1-QEk+0ZjvP/R3uMf5L2ROyCpc06Y=", "dev": true}, "is-svg": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/is-svg/-/is-svg-3.0.0.tgz", "integrity": "sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=", "requires": {"html-comment-regex": "^1.1.0"}}, "is-symbol": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/is-symbol/-/is-symbol-1.0.3.tgz", "integrity": "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=", "requires": {"has-symbols": "^1.0.1"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "optional": true}, "is-windows": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="}, "is-wsl": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="}, "isarray": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isexe": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isobject": {"version": "3.0.1", "resolved": "http://registry.npm.baidu-int.com/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "isstream": {"version": "0.1.2", "resolved": "http://registry.npm.baidu-int.com/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "optional": true}, "isurl": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/isurl/-/isurl-1.0.0.tgz", "integrity": "sha1-sn9PSfPNqj6kSgpbfzRi5u3DnWc=", "requires": {"has-to-string-tag-x": "^1.2.0", "is-object": "^1.0.1"}}, "js-tokens": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "js-yaml": {"version": "3.14.0", "resolved": "http://registry.npm.baidu-int.com/js-yaml/-/js-yaml-3.14.0.tgz", "integrity": "sha1-p6NBcPJqIbsWJCTYray0ETpp5II=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "optional": true}, "jsesc": {"version": "2.5.2", "resolved": "http://registry.npm.baidu-int.com/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="}, "json-buffer": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/json-buffer/-/json-buffer-3.0.0.tgz", "integrity": "sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg="}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="}, "json-schema": {"version": "0.2.3", "resolved": "http://registry.npm.baidu-int.com/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "optional": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://registry.npm.baidu-int.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stable-stringify": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "optional": true, "requires": {"jsonify": "~0.0.0"}}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://registry.npm.baidu-int.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "optional": true}, "json3": {"version": "3.3.3", "resolved": "http://registry.npm.baidu-int.com/json3/-/json3-3.3.3.tgz", "integrity": "sha512-c7/8mbUsKigAbLkD5B010BK4D9LZm7A1pNItkEwiUZRpIN66exu/e7YQWysGun+TRKaJp8MhemM+VkfWv42aCA=="}, "json5": {"version": "2.1.3", "resolved": "http://registry.npm.baidu-int.com/json5/-/json5-2.1.3.tgz", "integrity": "sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=", "requires": {"minimist": "^1.2.5"}}, "jsonify": {"version": "0.0.0", "resolved": "http://registry.npm.baidu-int.com/jsonify/-/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=", "optional": true}, "jsprim": {"version": "1.4.1", "resolved": "http://registry.npm.baidu-int.com/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "optional": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "keyv": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/keyv/-/keyv-3.0.0.tgz", "integrity": "sha512-eguHnq22OE3uVoSYG0LVWNP+4ppamWr9+zWBe1bsNcovIMy6huUJFPgy4mGwCd/rnl3vOLGW1MTlu4c57CT1xA==", "requires": {"json-buffer": "3.0.0"}}, "killable": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/killable/-/killable-1.0.1.tgz", "integrity": "sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg=="}, "kind-of": {"version": "6.0.3", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="}, "less": {"version": "2.7.3", "resolved": "http://registry.npm.baidu-int.com/less/-/less-2.7.3.tgz", "integrity": "sha1-zBJg9RyQCp7A2R+2mYE54CUHtjs=", "requires": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "source-map": "^0.5.3"}, "dependencies": {"ajv": {"version": "4.11.8", "resolved": "http://registry.npm.baidu-int.com/ajv/-/ajv-4.11.8.tgz", "integrity": "sha1-gv+wKynmYq5TvcIK8VlHcGc5xTY=", "optional": true, "requires": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}}, "assert-plus": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/assert-plus/-/assert-plus-0.2.0.tgz", "integrity": "sha1-104bh+ev/A24qttwIfP+SBAasjQ=", "optional": true}, "aws-sign2": {"version": "0.6.0", "resolved": "http://registry.npm.baidu-int.com/aws-sign2/-/aws-sign2-0.6.0.tgz", "integrity": "sha1-FDQt0428yU0OW4fXY81jYSwOeU8=", "optional": true}, "form-data": {"version": "2.1.4", "resolved": "http://registry.npm.baidu-int.com/form-data/-/form-data-2.1.4.tgz", "integrity": "sha1-M8GDrPGTJ27KqYFDpp6Uv+4XUNE=", "optional": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}}, "har-schema": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/har-schema/-/har-schema-1.0.5.tgz", "integrity": "sha1-0mMTX0MwfALGAq/I/pWXDAFRNp4=", "optional": true}, "har-validator": {"version": "4.2.1", "resolved": "http://registry.npm.baidu-int.com/har-validator/-/har-validator-4.2.1.tgz", "integrity": "sha1-M0gdDxu/9gDdID11gSpqX7oALio=", "optional": true, "requires": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}}, "http-signature": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/http-signature/-/http-signature-1.1.1.tgz", "integrity": "sha1-33LiZwZs0Kxn+3at+OE0qPvPkb8=", "optional": true, "requires": {"assert-plus": "^0.2.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "oauth-sign": {"version": "0.8.2", "resolved": "http://registry.npm.baidu-int.com/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM=", "optional": true}, "performance-now": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/performance-now/-/performance-now-0.2.0.tgz", "integrity": "sha1-M+8wxcd9TqIcWlOGnZG1bY8lVeU=", "optional": true}, "punycode": {"version": "1.4.1", "resolved": "http://registry.npm.baidu-int.com/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "optional": true}, "qs": {"version": "6.4.0", "resolved": "http://registry.npm.baidu-int.com/qs/-/qs-6.4.0.tgz", "integrity": "sha1-E+JtKK1rD/qpExLNO/cI7TUecjM=", "optional": true}, "request": {"version": "2.81.0", "resolved": "http://registry.npm.baidu-int.com/request/-/request-2.81.0.tgz", "integrity": "sha1-xpKJRqDgbF+Nb4qTM0af/aRimKA=", "optional": true, "requires": {"aws-sign2": "~0.6.0", "aws4": "^1.2.1", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~2.1.1", "har-validator": "~4.2.1", "hawk": "~3.1.3", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "performance-now": "^0.2.0", "qs": "~6.4.0", "safe-buffer": "^5.0.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "^0.6.0", "uuid": "^3.0.0"}}, "tough-cookie": {"version": "2.3.4", "resolved": "http://registry.npm.baidu-int.com/tough-cookie/-/tough-cookie-2.3.4.tgz", "integrity": "sha1-7GDO44rGdQY//JelwYlwV47oNlU=", "optional": true, "requires": {"punycode": "^1.4.1"}}}}, "less-loader": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/less-loader/-/less-loader-4.1.0.tgz", "integrity": "sha1-LBNSxbCaT4QQFJAnT9UWdN5BNj4=", "requires": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "less-plugin-functions": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/less-plugin-functions/-/less-plugin-functions-1.0.0.tgz", "integrity": "sha1-xP4Q9oGq4KgG2vn0EtIPAT4Q0kc="}, "leven": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/leven/-/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I="}, "levenary": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/levenary/-/levenary-1.1.1.tgz", "integrity": "sha1-hCqe6Y0gdap/ru2+MmeekgX0b3c=", "requires": {"leven": "^3.1.0"}}, "levn": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "listenercount": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/listenercount/-/listenercount-1.0.1.tgz", "integrity": "sha1-hMinKrWcRyUyFIDJdeZQg0LnCTc="}, "load-json-file": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/load-json-file/-/load-json-file-2.0.0.tgz", "integrity": "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}, "dependencies": {"parse-json": {"version": "2.2.0", "resolved": "http://registry.npm.baidu-int.com/parse-json/-/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "requires": {"error-ex": "^1.2.0"}}, "pify": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}}}, "loader-runner": {"version": "2.4.0", "resolved": "http://registry.npm.baidu-int.com/loader-runner/-/loader-runner-2.4.0.tgz", "integrity": "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c="}, "loader-utils": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/loader-utils/-/loader-utils-1.4.0.tgz", "integrity": "sha1-xXm140yzSxp07cbB+za/o3HVphM=", "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "dependencies": {"json5": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/json5/-/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "requires": {"minimist": "^1.2.0"}}}}, "locate-path": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.15", "resolved": "http://registry.npm.baidu-int.com/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="}, "lodash.memoize": {"version": "4.1.2", "resolved": "http://registry.npm.baidu-int.com/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "integrity": "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="}, "lodash.uniq": {"version": "4.5.0", "resolved": "http://registry.npm.baidu-int.com/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="}, "log-symbols": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/log-symbols/-/log-symbols-3.0.0.tgz", "integrity": "sha1-86CFFqXeqJMzan3uFNGKHP2rd8Q=", "requires": {"chalk": "^2.4.2"}}, "loglevel": {"version": "1.6.8", "resolved": "http://registry.npm.baidu-int.com/loglevel/-/loglevel-1.6.8.tgz", "integrity": "sha1-iiX7ddCSIw7NRFcnDYC1TigBEXE="}, "loose-envify": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lower-case": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/lower-case/-/lower-case-2.0.1.tgz", "integrity": "sha1-Oe6zbjlhFcwF4pQi6uqeaSyUCMc=", "requires": {"tslib": "^1.10.0"}}, "lowercase-keys": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/lowercase-keys/-/lowercase-keys-1.0.1.tgz", "integrity": "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8="}, "lru-cache": {"version": "5.1.1", "resolved": "http://registry.npm.baidu-int.com/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/make-dir/-/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "requires": {"pify": "^4.0.1", "semver": "^5.6.0"}}, "map-cache": {"version": "0.2.2", "resolved": "http://registry.npm.baidu-int.com/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="}, "map-visit": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "requires": {"object-visit": "^1.0.0"}}, "md5.js": {"version": "1.3.5", "resolved": "http://registry.npm.baidu-int.com/md5.js/-/md5.js-1.3.5.tgz", "integrity": "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==", "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "mdn-data": {"version": "2.0.4", "resolved": "http://registry.npm.baidu-int.com/mdn-data/-/mdn-data-2.0.4.tgz", "integrity": "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA=="}, "media-typer": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "memory-fs": {"version": "0.4.1", "resolved": "http://registry.npm.baidu-int.com/memory-fs/-/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "merge-descriptors": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "merge2": {"version": "1.4.1", "resolved": "http://registry.npm.baidu-int.com/merge2/-/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4="}, "methods": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "3.1.10", "resolved": "http://registry.npm.baidu-int.com/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "mime": {"version": "1.6.0", "resolved": "http://registry.npm.baidu-int.com/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="}, "mime-db": {"version": "1.44.0", "resolved": "http://registry.npm.baidu-int.com/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I="}, "mime-types": {"version": "2.1.27", "resolved": "http://registry.npm.baidu-int.com/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "requires": {"mime-db": "1.44.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "mimic-response": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/mimic-response/-/mimic-response-1.0.1.tgz", "integrity": "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ=="}, "mini-css-extract-plugin": {"version": "0.5.0", "resolved": "http://registry.npm.baidu-int.com/mini-css-extract-plugin/-/mini-css-extract-plugin-0.5.0.tgz", "integrity": "sha1-rABZsCuWklFaY3EVsMyf7To1x7A=", "requires": {"loader-utils": "^1.1.0", "schema-utils": "^1.0.0", "webpack-sources": "^1.1.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "minimalistic-assert": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo="}, "minimatch": {"version": "3.0.4", "resolved": "http://registry.npm.baidu-int.com/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://registry.npm.baidu-int.com/minimist/-/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "mississippi": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/mississippi/-/mississippi-3.0.0.tgz", "integrity": "sha1-6goykfl+C16HdrNj1fChLZTGcCI=", "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "mixin-deep": {"version": "1.3.2", "resolved": "http://registry.npm.baidu-int.com/mixin-deep/-/mixin-deep-1.3.2.tgz", "integrity": "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==", "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdir-recursive": {"version": "0.4.0", "resolved": "http://registry.npm.baidu-int.com/mkdir-recursive/-/mkdir-recursive-0.4.0.tgz", "integrity": "sha512-gbTtiEu8P/GSMh1lAa0YYNr8XIfDzFgnWtetw3Hfz9nw6YXySHNYOZF/uUTgyp8GHvFnNw/EG7VhOkD6zfVb6A=="}, "mkdirp": {"version": "0.5.5", "resolved": "http://registry.npm.baidu-int.com/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "requires": {"minimist": "^1.2.5"}}, "move-concurrently": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/move-concurrently/-/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "ms": {"version": "2.1.2", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "multicast-dns": {"version": "6.2.3", "resolved": "http://registry.npm.baidu-int.com/multicast-dns/-/multicast-dns-6.2.3.tgz", "integrity": "sha1-oOx72QVcQoL3kMPIL04o2zsxsik=", "requires": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}}, "multicast-dns-service-types": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz", "integrity": "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="}, "mute-stream": {"version": "0.0.8", "resolved": "http://registry.npm.baidu-int.com/mute-stream/-/mute-stream-0.0.8.tgz", "integrity": "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="}, "nan": {"version": "2.14.1", "resolved": "http://registry.npm.baidu-int.com/nan/-/nan-2.14.1.tgz", "integrity": "sha1-174036MQW5FJTDFHCJMV7/iHSwE=", "optional": true}, "nanomatch": {"version": "1.2.13", "resolved": "http://registry.npm.baidu-int.com/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "natural-compare": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "ncp": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ncp/-/ncp-2.0.0.tgz", "integrity": "sha1-GVoh1sRuNh0vsSgbo4uR6d9727M="}, "negotiator": {"version": "0.6.2", "resolved": "http://registry.npm.baidu-int.com/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "neo-async": {"version": "2.6.1", "resolved": "http://registry.npm.baidu-int.com/neo-async/-/neo-async-2.6.1.tgz", "integrity": "sha1-rCetpmFn+ohJpq3dg39rGJrSCBw="}, "netutil": {"version": "0.0.2", "resolved": "http://registry.npm.baidu-int.com/netutil/-/netutil-0.0.2.tgz", "integrity": "sha1-qAOQtAMvtwIscP9cDzlRYHFmvu0="}, "nice-try": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/nice-try/-/nice-try-1.0.5.tgz", "integrity": "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="}, "no-case": {"version": "3.0.3", "resolved": "http://registry.npm.baidu-int.com/no-case/-/no-case-3.0.3.tgz", "integrity": "sha1-whtDTB/+SLOQh+hs+00lgunfGPg=", "requires": {"lower-case": "^2.0.1", "tslib": "^1.10.0"}}, "node-forge": {"version": "0.9.0", "resolved": "http://registry.npm.baidu-int.com/node-forge/-/node-forge-0.9.0.tgz", "integrity": "sha512-7ASaDa3pD+lJ3WvXFsxekJQelBKRpne+GOVbLbtHYdd7pFspyeuJHnWfLplGf3SwKGbfs/aYl5V/JCIaHVUKKQ=="}, "node-libs-browser": {"version": "2.2.1", "resolved": "http://registry.npm.baidu-int.com/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "integrity": "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==", "requires": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "dependencies": {"buffer": {"version": "4.9.2", "resolved": "http://registry.npm.baidu-int.com/buffer/-/buffer-4.9.2.tgz", "integrity": "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==", "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "punycode": {"version": "1.4.1", "resolved": "http://registry.npm.baidu-int.com/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}}}, "node-releases": {"version": "1.1.58", "resolved": "http://registry.npm.baidu-int.com/node-releases/-/node-releases-1.1.58.tgz", "integrity": "sha1-juIO7zD6YOUnVfzAlC3vWnNP6TU="}, "normalize-package-data": {"version": "2.5.0", "resolved": "http://registry.npm.baidu-int.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="}, "normalize-range": {"version": "0.1.2", "resolved": "http://registry.npm.baidu-int.com/normalize-range/-/normalize-range-0.1.2.tgz", "integrity": "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="}, "normalize-url": {"version": "3.3.0", "resolved": "http://registry.npm.baidu-int.com/normalize-url/-/normalize-url-3.3.0.tgz", "integrity": "sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg=="}, "npm-conf": {"version": "1.1.3", "resolved": "http://registry.npm.baidu-int.com/npm-conf/-/npm-conf-1.1.3.tgz", "integrity": "sha1-JWzEe9DiGMJZxOlVC/QTvCGSr/k=", "requires": {"config-chain": "^1.1.11", "pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "npm-run-path": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/npm-run-path/-/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "requires": {"path-key": "^2.0.0"}}, "nth-check": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/nth-check/-/nth-check-1.0.2.tgz", "integrity": "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==", "requires": {"boolbase": "~1.0.0"}}, "num2fraction": {"version": "1.2.2", "resolved": "http://registry.npm.baidu-int.com/num2fraction/-/num2fraction-1.2.2.tgz", "integrity": "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4="}, "object-assign": {"version": "4.1.1", "resolved": "http://registry.npm.baidu-int.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-copy": {"version": "0.1.0", "resolved": "http://registry.npm.baidu-int.com/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "object-inspect": {"version": "1.8.0", "resolved": "http://registry.npm.baidu-int.com/object-inspect/-/object-inspect-1.8.0.tgz", "integrity": "sha1-34B+Xs9TpgnMa/6T6sPMe+WzqdA="}, "object-is": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/object-is/-/object-is-1.1.2.tgz", "integrity": "sha1-xdLof/nhGfeLegiEQVGeLuwVc7Y=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "object-keys": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="}, "object-visit": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/object.assign/-/object.assign-4.1.0.tgz", "integrity": "sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=", "requires": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}}, "object.entries": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/object.entries/-/object.entries-1.1.2.tgz", "integrity": "sha1-vHPwCstra7FsIDQ0sQ+afnl9Ot0=", "dev": true, "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5", "has": "^1.0.3"}}, "object.getownpropertydescriptors": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.0.tgz", "integrity": "sha1-Npvx+VktiridcS3O1cuBx8U1Jkk=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1"}}, "object.pick": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "requires": {"isobject": "^3.0.1"}}, "object.values": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/object.values/-/object.values-1.1.1.tgz", "integrity": "sha1-aKmezeNWt+kpWjxeDOMdyMlT3l4=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1", "function-bind": "^1.1.1", "has": "^1.0.3"}}, "obuf": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/obuf/-/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="}, "on-finished": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="}, "once": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "http://registry.npm.baidu-int.com/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "requires": {"mimic-fn": "^2.1.0"}}, "opn": {"version": "6.0.0", "resolved": "http://registry.npm.baidu-int.com/opn/-/opn-6.0.0.tgz", "integrity": "sha1-PFsNtnbV+X2hIz0e1C0YK8WifS0=", "requires": {"is-wsl": "^1.1.0"}}, "optionator": {"version": "0.8.3", "resolved": "http://registry.npm.baidu-int.com/optionator/-/optionator-0.8.3.tgz", "integrity": "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==", "dev": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}}, "original": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/original/-/original-1.0.2.tgz", "integrity": "sha512-hyBVl6iqqUOJ8FqRe+l/gS8H+kKYjrEndd5Pm1MfBtsEKA038HkkdbAl/72EAXGyonD/PFsvmVG+EvcIpliMBg==", "requires": {"url-parse": "^1.4.3"}}, "os-browserify": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc="}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="}, "p-cancelable": {"version": "0.4.1", "resolved": "http://registry.npm.baidu-int.com/p-cancelable/-/p-cancelable-0.4.1.tgz", "integrity": "sha512-HNa1A8LvB1kie7cERyy21VNeHb2CWJJYqyyC2o3klWFfMGlFmWv2Z7sFgZH8ZiaYL95ydToKTFVXgMV/Os0bBQ=="}, "p-each-series": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/p-each-series/-/p-each-series-1.0.0.tgz", "integrity": "sha1-kw89Et0fUOdDRFeiLNbwSsatf3E=", "requires": {"p-reduce": "^1.0.0"}}, "p-event": {"version": "2.3.1", "resolved": "http://registry.npm.baidu-int.com/p-event/-/p-event-2.3.1.tgz", "integrity": "sha1-WWJ57xaassPgyuiMHPuwgHmZPvY=", "requires": {"p-timeout": "^2.0.1"}}, "p-finally": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="}, "p-is-promise": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/p-is-promise/-/p-is-promise-1.1.0.tgz", "integrity": "sha1-nJRWmJ6fZYgBewQ01WCXZ1w9oF4="}, "p-limit": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "requires": {"p-limit": "^2.0.0"}}, "p-map": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/p-map/-/p-map-2.1.0.tgz", "integrity": "sha1-MQko/u+cnsxltosXaTAYpmXOoXU="}, "p-reduce": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/p-reduce/-/p-reduce-1.0.0.tgz", "integrity": "sha1-GMKw3ZNqRpClKfgjH1ig/bakffo="}, "p-retry": {"version": "3.0.1", "resolved": "http://registry.npm.baidu-int.com/p-retry/-/p-retry-3.0.1.tgz", "integrity": "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=", "requires": {"retry": "^0.12.0"}}, "p-timeout": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/p-timeout/-/p-timeout-2.0.1.tgz", "integrity": "sha1-2N0ZeVldLcATnh/ka4tkbLPN8Dg=", "requires": {"p-finally": "^1.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://registry.npm.baidu-int.com/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="}, "pako": {"version": "1.0.11", "resolved": "http://registry.npm.baidu-int.com/pako/-/pako-1.0.11.tgz", "integrity": "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8="}, "parallel-transform": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/parallel-transform/-/parallel-transform-1.2.0.tgz", "integrity": "sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==", "requires": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "param-case": {"version": "3.0.3", "resolved": "http://registry.npm.baidu-int.com/param-case/-/param-case-3.0.3.tgz", "integrity": "sha1-S+Qfg5nv9iHFbuu4KaXkUdmAEjg=", "requires": {"dot-case": "^3.0.3", "tslib": "^1.10.0"}}, "parent-module": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "requires": {"callsites": "^3.0.0"}, "dependencies": {"callsites": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true}}}, "parse-asn1": {"version": "5.1.5", "resolved": "http://registry.npm.baidu-int.com/parse-asn1/-/parse-asn1-5.1.5.tgz", "integrity": "sha512-jkMYn1dcJqF6d5CpU689bq7w/b5ALS9ROVSpQDPrZsqqesUJii9qutvoT5ltGedNXMO2e16YUWIghG9KxaViTQ==", "requires": {"asn1.js": "^4.0.0", "browserify-aes": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}}, "parse-json": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "parse-passwd": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY="}, "parseurl": {"version": "1.3.3", "resolved": "http://registry.npm.baidu-int.com/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "pascal-case": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/pascal-case/-/pascal-case-3.1.1.tgz", "integrity": "sha1-WsGXUTPtYZKB6Ikglz0s0fJ53l8=", "requires": {"no-case": "^3.0.3", "tslib": "^1.10.0"}}, "pascalcase": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="}, "path-browserify": {"version": "0.0.1", "resolved": "http://registry.npm.baidu-int.com/path-browserify/-/path-browserify-0.0.1.tgz", "integrity": "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ=="}, "path-dirname": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="}, "path-exists": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="}, "path-key": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/path-key/-/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="}, "path-parse": {"version": "1.0.6", "resolved": "http://registry.npm.baidu-int.com/path-parse/-/path-parse-1.0.6.tgz", "integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw=="}, "path-to-regexp": {"version": "6.1.0", "resolved": "http://registry.npm.baidu-int.com/path-to-regexp/-/path-to-regexp-6.1.0.tgz", "integrity": "sha1-Cxj4i3oM4L+uaiWZDJCauG9RJCc="}, "path-type": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/path-type/-/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "pbkdf2": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/pbkdf2/-/pbkdf2-3.1.1.tgz", "integrity": "sha1-y4cksPramEWWhW0abrr9NYRlS5Q=", "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "pend": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA="}, "picomatch": {"version": "2.2.2", "resolved": "http://registry.npm.baidu-int.com/picomatch/-/picomatch-2.2.2.tgz", "integrity": "sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=", "optional": true}, "pify": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-4.0.1.tgz", "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="}, "pinkie": {"version": "2.0.4", "resolved": "http://registry.npm.baidu-int.com/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="}, "pinkie-promise": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==", "requires": {"find-up": "^3.0.0"}}, "plur": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/plur/-/plur-3.1.1.tgz", "integrity": "sha1-YCZ5Z4ZqjYEVBP5Y8vqrojdUals=", "dev": true, "requires": {"irregular-plurals": "^2.0.0"}}, "pnp-webpack-plugin": {"version": "1.6.4", "resolved": "http://registry.npm.baidu-int.com/pnp-webpack-plugin/-/pnp-webpack-plugin-1.6.4.tgz", "integrity": "sha1-yXEaxNxIpoXauvyG+Lbdn434QUk=", "requires": {"ts-pnp": "^1.1.6"}}, "portfinder": {"version": "1.0.26", "resolved": "http://registry.npm.baidu-int.com/portfinder/-/portfinder-1.0.26.tgz", "integrity": "sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA=", "requires": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.1"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}}}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://registry.npm.baidu-int.com/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="}, "postcss": {"version": "7.0.32", "resolved": "http://registry.npm.baidu-int.com/postcss/-/postcss-7.0.32.tgz", "integrity": "sha1-QxDW7jRwU9o0M9sr5JKIPWLOxZ0=", "requires": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}, "supports-color": {"version": "6.1.0", "resolved": "http://registry.npm.baidu-int.com/supports-color/-/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "requires": {"has-flag": "^3.0.0"}}}}, "postcss-calc": {"version": "7.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-calc/-/postcss-calc-7.0.2.tgz", "integrity": "sha1-UE780AjKAnMSBWiweSsWzc3oqsE=", "requires": {"postcss": "^7.0.27", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.0.2"}}, "postcss-colormin": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/postcss-colormin/-/postcss-colormin-4.0.3.tgz", "integrity": "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=", "requires": {"browserslist": "^4.0.0", "color": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-convert-values": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz", "integrity": "sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ==", "requires": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-discard-comments": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz", "integrity": "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=", "requires": {"postcss": "^7.0.0"}}, "postcss-discard-duplicates": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz", "integrity": "sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ==", "requires": {"postcss": "^7.0.0"}}, "postcss-discard-empty": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz", "integrity": "sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w==", "requires": {"postcss": "^7.0.0"}}, "postcss-discard-overridden": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz", "integrity": "sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg==", "requires": {"postcss": "^7.0.0"}}, "postcss-load-config": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/postcss-load-config/-/postcss-load-config-2.1.0.tgz", "integrity": "sha512-4pV3JJVPLd5+RueiVVB+gFOAa7GWc25XQcMp86Zexzke69mKf6Nx9LRcQywdz7yZI9n1udOxmLuAwTBypypF8Q==", "requires": {"cosmiconfig": "^5.0.0", "import-cwd": "^2.0.0"}}, "postcss-loader": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/postcss-loader/-/postcss-loader-3.0.0.tgz", "integrity": "sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA==", "requires": {"loader-utils": "^1.1.0", "postcss": "^7.0.0", "postcss-load-config": "^2.0.0", "schema-utils": "^1.0.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "postcss-merge-longhand": {"version": "4.0.11", "resolved": "http://registry.npm.baidu-int.com/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz", "integrity": "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=", "requires": {"css-color-names": "0.0.4", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "stylehacks": "^4.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-merge-rules": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz", "integrity": "sha1-NivqT/Wh+Y5AdacTxsslrv75plA=", "requires": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "cssnano-util-same-parent": "^4.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0", "vendors": "^1.0.0"}, "dependencies": {"postcss-selector-parser": {"version": "3.1.2", "resolved": "http://registry.npm.baidu-int.com/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "integrity": "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=", "requires": {"dot-prop": "^5.2.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}}}, "postcss-minify-font-values": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz", "integrity": "sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg==", "requires": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-minify-gradients": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz", "integrity": "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=", "requires": {"cssnano-util-get-arguments": "^4.0.0", "is-color-stop": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-minify-params": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz", "integrity": "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=", "requires": {"alphanum-sort": "^1.0.0", "browserslist": "^4.0.0", "cssnano-util-get-arguments": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "uniqs": "^2.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-minify-selectors": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz", "integrity": "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=", "requires": {"alphanum-sort": "^1.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0"}, "dependencies": {"postcss-selector-parser": {"version": "3.1.2", "resolved": "http://registry.npm.baidu-int.com/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "integrity": "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=", "requires": {"dot-prop": "^5.2.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}}}, "postcss-modules-extract-imports": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/postcss-modules-extract-imports/-/postcss-modules-extract-imports-2.0.0.tgz", "integrity": "sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ==", "requires": {"postcss": "^7.0.5"}}, "postcss-modules-local-by-default": {"version": "2.0.6", "resolved": "http://registry.npm.baidu-int.com/postcss-modules-local-by-default/-/postcss-modules-local-by-default-2.0.6.tgz", "integrity": "sha1-3ZlT9t1Ha1/R7y2IMMiSl2C1bmM=", "requires": {"postcss": "^7.0.6", "postcss-selector-parser": "^6.0.0", "postcss-value-parser": "^3.3.1"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-modules-scope": {"version": "2.2.0", "resolved": "http://registry.npm.baidu-int.com/postcss-modules-scope/-/postcss-modules-scope-2.2.0.tgz", "integrity": "sha1-OFyuATzHdD9afXYC0Qc6iequYu4=", "requires": {"postcss": "^7.0.6", "postcss-selector-parser": "^6.0.0"}}, "postcss-modules-values": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/postcss-modules-values/-/postcss-modules-values-2.0.0.tgz", "integrity": "sha1-R5tG3Axco9x/pScIUYNrnscVL2Q=", "requires": {"icss-replace-symbols": "^1.1.0", "postcss": "^7.0.6"}}, "postcss-normalize-charset": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz", "integrity": "sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g==", "requires": {"postcss": "^7.0.0"}}, "postcss-normalize-display-values": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz", "integrity": "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=", "requires": {"cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-positions": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz", "integrity": "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=", "requires": {"cssnano-util-get-arguments": "^4.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-repeat-style": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz", "integrity": "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=", "requires": {"cssnano-util-get-arguments": "^4.0.0", "cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-string": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz", "integrity": "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=", "requires": {"has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-timing-functions": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz", "integrity": "sha1-jgCcoqOUnNr4rSPmtquZy159KNk=", "requires": {"cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-unicode": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz", "integrity": "sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg==", "requires": {"browserslist": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-url": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz", "integrity": "sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA==", "requires": {"is-absolute-url": "^2.0.0", "normalize-url": "^3.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-normalize-whitespace": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz", "integrity": "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=", "requires": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-ordered-values": {"version": "4.1.2", "resolved": "http://registry.npm.baidu-int.com/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz", "integrity": "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=", "requires": {"cssnano-util-get-arguments": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-reduce-initial": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz", "integrity": "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=", "requires": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0"}}, "postcss-reduce-transforms": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz", "integrity": "sha1-F++kBerMbge+NBSlyi0QdGgdTik=", "requires": {"cssnano-util-get-match": "^4.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-selector-parser": {"version": "6.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-selector-parser/-/postcss-selector-parser-6.0.2.tgz", "integrity": "sha1-k0z3mdAWyDQRhZ4J3Oyt4BKG7Fw=", "requires": {"cssesc": "^3.0.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}, "postcss-svgo": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/postcss-svgo/-/postcss-svgo-4.0.2.tgz", "integrity": "sha1-F7mXvHEbMzurFDqu07jT1uPTglg=", "requires": {"is-svg": "^3.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "svgo": "^1.0.0"}, "dependencies": {"postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="}}}, "postcss-unique-selectors": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz", "integrity": "sha512-+<PERSON><PERSON><PERSON><PERSON>o9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg==", "requires": {"alphanum-sort": "^1.0.0", "postcss": "^7.0.0", "uniqs": "^2.0.0"}}, "postcss-value-parser": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/postcss-value-parser/-/postcss-value-parser-4.1.0.tgz", "integrity": "sha1-RD9qIM7WSBor2k+oUypuVdeJoss="}, "prelude-ls": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true}, "prepend-http": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/prepend-http/-/prepend-http-2.0.0.tgz", "integrity": "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc="}, "pretty-error": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/pretty-error/-/pretty-error-2.1.1.tgz", "integrity": "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=", "requires": {"renderkid": "^2.0.1", "utila": "~0.4"}}, "process": {"version": "0.11.10", "resolved": "http://registry.npm.baidu-int.com/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI="}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "progress": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/progress/-/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=", "dev": true}, "promise": {"version": "7.3.1", "resolved": "http://registry.npm.baidu-int.com/promise/-/promise-7.3.1.tgz", "integrity": "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=", "optional": true, "requires": {"asap": "~2.0.3"}}, "promise-inflight": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM="}, "proto-list": {"version": "1.2.4", "resolved": "http://registry.npm.baidu-int.com/proto-list/-/proto-list-1.2.4.tgz", "integrity": "sha1-IS1b/hMYMGpCD2QCuOJv85ZHqEk="}, "proxy-addr": {"version": "2.0.6", "resolved": "http://registry.npm.baidu-int.com/proxy-addr/-/proxy-addr-2.0.6.tgz", "integrity": "sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=", "requires": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}}, "prr": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/prr/-/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY="}, "public-encrypt": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/public-encrypt/-/public-encrypt-4.0.3.tgz", "integrity": "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==", "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://registry.npm.baidu-int.com/bn.js/-/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg="}}}, "pump": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/pump/-/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "resolved": "http://registry.npm.baidu-int.com/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==", "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/pump/-/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "punycode": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="}, "q": {"version": "1.5.1", "resolved": "http://registry.npm.baidu-int.com/q/-/q-1.5.1.tgz", "integrity": "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc="}, "query-string": {"version": "5.1.1", "resolved": "http://registry.npm.baidu-int.com/query-string/-/query-string-5.1.1.tgz", "integrity": "sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s=", "requires": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}}, "querystring": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="}, "querystring-es3": {"version": "0.2.1", "resolved": "http://registry.npm.baidu-int.com/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM="}, "querystringify": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/querystringify/-/querystringify-2.1.1.tgz", "integrity": "sha1-YOWl/WSn+L+k0qsu1v30yFutFU4="}, "randombytes": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "range-parser": {"version": "1.2.1", "resolved": "http://registry.npm.baidu-int.com/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "raw-body": {"version": "2.4.0", "resolved": "http://registry.npm.baidu-int.com/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha1-oc5vucm8NWylLoklarWQWeE9AzI=", "requires": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/bytes/-/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}}}, "raw-loader": {"version": "0.5.1", "resolved": "http://registry.npm.baidu-int.com/raw-loader/-/raw-loader-0.5.1.tgz", "integrity": "sha1-DD0L6u2KAclm2Xh793goElKpeao="}, "read-pkg": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/read-pkg/-/read-pkg-2.0.0.tgz", "integrity": "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=", "dev": true, "requires": {"load-json-file": "^2.0.0", "normalize-package-data": "^2.3.2", "path-type": "^2.0.0"}, "dependencies": {"path-type": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/path-type/-/path-type-2.0.0.tgz", "integrity": "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=", "dev": true, "requires": {"pify": "^2.0.0"}}, "pify": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}}}, "read-pkg-up": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/read-pkg-up/-/read-pkg-up-2.0.0.tgz", "integrity": "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^2.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/find-up/-/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/p-limit/-/p-limit-1.3.0.tgz", "integrity": "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/p-locate/-/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/p-try/-/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}}}, "readable-stream": {"version": "2.3.7", "resolved": "http://registry.npm.baidu-int.com/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "3.4.0", "resolved": "http://registry.npm.baidu-int.com/readdirp/-/readdirp-3.4.0.tgz", "integrity": "sha1-n9zN+ekVWAVEkiGsZF6DA6tbmto=", "optional": true, "requires": {"picomatch": "^2.2.1"}}, "rechoir": {"version": "0.6.2", "resolved": "http://registry.npm.baidu-int.com/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=", "requires": {"resolve": "^1.1.6"}}, "regenerate": {"version": "1.4.1", "resolved": "http://registry.npm.baidu-int.com/regenerate/-/regenerate-1.4.1.tgz", "integrity": "sha1-ytkq2Oa1kXc0hfvgWkhcr09Ffm8="}, "regenerate-unicode-properties": {"version": "8.2.0", "resolved": "http://registry.npm.baidu-int.com/regenerate-unicode-properties/-/regenerate-unicode-properties-8.2.0.tgz", "integrity": "sha1-5d5xEdZV57pgwFfb6f83yH5lzew=", "requires": {"regenerate": "^1.4.0"}}, "regenerator-runtime": {"version": "0.13.5", "resolved": "http://registry.npm.baidu-int.com/regenerator-runtime/-/regenerator-runtime-0.13.5.tgz", "integrity": "sha1-2Hih0JS0MG0QuQlkhLM+vVXiZpc="}, "regenerator-transform": {"version": "0.14.5", "resolved": "http://registry.npm.baidu-int.com/regenerator-transform/-/regenerator-transform-0.14.5.tgz", "integrity": "sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=", "requires": {"@babel/runtime": "^7.8.4"}}, "regex-not": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexp-tree": {"version": "0.1.21", "resolved": "http://registry.npm.baidu-int.com/regexp-tree/-/regexp-tree-0.1.21.tgz", "integrity": "sha1-VeIka399NvG0YUkJQvp4ApnEANc="}, "regexp.prototype.flags": {"version": "1.3.0", "resolved": "http://registry.npm.baidu-int.com/regexp.prototype.flags/-/regexp.prototype.flags-1.3.0.tgz", "integrity": "sha1-erqJs8E6ZFCdq888qNn7ub31y3U=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.0-next.1"}}, "regexpp": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/regexpp/-/regexpp-2.0.1.tgz", "integrity": "sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==", "dev": true}, "regexpu-core": {"version": "4.7.0", "resolved": "http://registry.npm.baidu-int.com/regexpu-core/-/regexpu-core-4.7.0.tgz", "integrity": "sha1-/L9FjFBDGwu3tF1pZ7gZLZHz2Tg=", "requires": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^8.2.0", "regjsgen": "^0.5.1", "regjsparser": "^0.6.4", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.2.0"}}, "regjsgen": {"version": "0.5.2", "resolved": "http://registry.npm.baidu-int.com/regjsgen/-/regjsgen-0.5.2.tgz", "integrity": "sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM="}, "regjsparser": {"version": "0.6.4", "resolved": "http://registry.npm.baidu-int.com/regjsparser/-/regjsparser-0.6.4.tgz", "integrity": "sha1-p2n4aEMIQBpm6bUp0kNv9NBmYnI=", "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "http://registry.npm.baidu-int.com/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="}}}, "relateurl": {"version": "0.2.7", "resolved": "http://registry.npm.baidu-int.com/relateurl/-/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="}, "renderkid": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/renderkid/-/renderkid-2.0.3.tgz", "integrity": "sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk=", "requires": {"css-select": "^1.1.0", "dom-converter": "^0.2", "htmlparser2": "^3.3.0", "strip-ansi": "^3.0.0", "utila": "^0.4.0"}, "dependencies": {"css-select": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/css-select/-/css-select-1.2.0.tgz", "integrity": "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=", "requires": {"boolbase": "~1.0.0", "css-what": "2.1", "domutils": "1.5.1", "nth-check": "~1.0.1"}}, "css-what": {"version": "2.1.3", "resolved": "http://registry.npm.baidu-int.com/css-what/-/css-what-2.1.3.tgz", "integrity": "sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI="}, "domutils": {"version": "1.5.1", "resolved": "http://registry.npm.baidu-int.com/domutils/-/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "requires": {"dom-serializer": "0", "domelementtype": "1"}}}}, "repeat-element": {"version": "1.1.3", "resolved": "http://registry.npm.baidu-int.com/repeat-element/-/repeat-element-1.1.3.tgz", "integrity": "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g=="}, "repeat-string": {"version": "1.6.1", "resolved": "http://registry.npm.baidu-int.com/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc="}, "require-directory": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="}, "require-main-filename": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/require-main-filename/-/require-main-filename-2.0.0.tgz", "integrity": "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="}, "requires-port": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resolve": {"version": "1.17.0", "resolved": "http://registry.npm.baidu-int.com/resolve/-/resolve-1.17.0.tgz", "integrity": "sha1-sllBtUloIxzC0bt2p5y38sC/hEQ=", "requires": {"path-parse": "^1.0.6"}}, "resolve-cwd": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "requires": {"resolve-from": "^3.0.0"}}, "resolve-dir": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=", "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "dependencies": {"global-modules": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=", "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}}}, "resolve-from": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g="}, "resolve-url": {"version": "0.2.1", "resolved": "http://registry.npm.baidu-int.com/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="}, "responselike": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/responselike/-/responselike-1.0.2.tgz", "integrity": "sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec=", "requires": {"lowercase-keys": "^1.0.0"}}, "restore-cursor": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/restore-cursor/-/restore-cursor-3.1.0.tgz", "integrity": "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=", "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "ret": {"version": "0.1.15", "resolved": "http://registry.npm.baidu-int.com/ret/-/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="}, "retry": {"version": "0.12.0", "resolved": "http://registry.npm.baidu-int.com/retry/-/retry-0.12.0.tgz", "integrity": "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="}, "rgb-regex": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/rgb-regex/-/rgb-regex-1.0.1.tgz", "integrity": "sha1-wODWiC3w4jviVKR16O3UGRX+rrE="}, "rgba-regex": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/rgba-regex/-/rgba-regex-1.0.0.tgz", "integrity": "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM="}, "rimraf": {"version": "2.7.1", "resolved": "http://registry.npm.baidu-int.com/rimraf/-/rimraf-2.7.1.tgz", "integrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==", "requires": {"glob": "^7.1.3"}}, "ripemd160": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/ripemd160/-/ripemd160-2.0.2.tgz", "integrity": "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==", "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "run-async": {"version": "2.4.1", "resolved": "http://registry.npm.baidu-int.com/run-async/-/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="}, "run-queue": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/run-queue/-/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "requires": {"aproba": "^1.1.1"}}, "rxjs": {"version": "6.6.0", "resolved": "http://registry.npm.baidu-int.com/rxjs/-/rxjs-6.6.0.tgz", "integrity": "sha1-rykB7t8C46g/+n+IYkD/kBi77IQ=", "requires": {"tslib": "^1.9.0"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://registry.npm.baidu-int.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "safe-regex": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://registry.npm.baidu-int.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "sax": {"version": "1.2.4", "resolved": "http://registry.npm.baidu-int.com/sax/-/sax-1.2.4.tgz", "integrity": "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="}, "schema-utils": {"version": "2.7.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-2.7.0.tgz", "integrity": "sha1-FxUfdtjq5n+793lgwzxnatn078c=", "requires": {"@types/json-schema": "^7.0.4", "ajv": "^6.12.2", "ajv-keywords": "^3.4.1"}}, "script-loader": {"version": "0.7.2", "resolved": "http://registry.npm.baidu-int.com/script-loader/-/script-loader-0.7.2.tgz", "integrity": "sha1-IBbbb4byX1z1baOJFdgzeLsWa6c=", "requires": {"raw-loader": "~0.5.1"}}, "seek-bzip": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/seek-bzip/-/seek-bzip-1.0.5.tgz", "integrity": "sha1-z+kXyz0nS8/6x5J1ivUxc+sfq9w=", "requires": {"commander": "~2.8.1"}, "dependencies": {"commander": {"version": "2.8.1", "resolved": "http://registry.npm.baidu-int.com/commander/-/commander-2.8.1.tgz", "integrity": "sha1-Br42f+v9oMMwqh4qBy09yXYkJdQ=", "requires": {"graceful-readlink": ">= 1.0.0"}}}}, "select-hose": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/select-hose/-/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="}, "selfsigned": {"version": "1.10.7", "resolved": "http://registry.npm.baidu-int.com/selfsigned/-/selfsigned-1.10.7.tgz", "integrity": "sha512-8M3wBCzeWIJnQfl43IKwOmC4H/RAp50S8DF60znzjW5GVqTcSe2vWclt7hmYVPkKPlHWOu5EaWOMZ2Y6W8ZXTA==", "requires": {"node-forge": "0.9.0"}}, "semver": {"version": "5.7.1", "resolved": "http://registry.npm.baidu-int.com/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}, "semver-try-require": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/semver-try-require/-/semver-try-require-3.0.0.tgz", "integrity": "sha1-O+EFELo+4D2q1+RE01y3Ry9/jWo=", "requires": {"semver": "6.2.0"}, "dependencies": {"semver": {"version": "6.2.0", "resolved": "http://registry.npm.baidu-int.com/semver/-/semver-6.2.0.tgz", "integrity": "sha512-jdFC1VdUGT/2Scgbimf7FSx9iJLXoqfglSF+gJeuNWVpiE37OIbc1jywR/GJyFdz3mnkz2/id0L0J/cr0izR5A=="}}}, "send": {"version": "0.17.1", "resolved": "http://registry.npm.baidu-int.com/send/-/send-0.17.1.tgz", "integrity": "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=", "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "ms": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="}}}, "serialize-javascript": {"version": "1.9.1", "resolved": "http://registry.npm.baidu-int.com/serialize-javascript/-/serialize-javascript-1.9.1.tgz", "integrity": "sha512-0Vb/54WJ6k5v8sSWN09S0ora+Hnr+cX40r9F170nT+mSkaxltoE/7R3OrIdBSUv1OoiobH1QoWQbCnAO+e8J1A=="}, "serve-index": {"version": "1.9.1", "resolved": "http://registry.npm.baidu-int.com/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "http-errors": {"version": "1.6.3", "resolved": "http://registry.npm.baidu-int.com/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "inherits": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "setprototypeof": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="}}}, "serve-static": {"version": "1.14.1", "resolved": "http://registry.npm.baidu-int.com/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="}, "set-value": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/set-value/-/set-value-2.0.1.tgz", "integrity": "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "http://registry.npm.baidu-int.com/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="}, "setprototypeof": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "sha.js": {"version": "2.4.11", "resolved": "http://registry.npm.baidu-int.com/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shebang-command": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="}, "shelljs": {"version": "0.8.4", "resolved": "http://registry.npm.baidu-int.com/shelljs/-/shelljs-0.8.4.tgz", "integrity": "sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=", "requires": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}}, "signal-exit": {"version": "3.0.3", "resolved": "http://registry.npm.baidu-int.com/signal-exit/-/signal-exit-3.0.3.tgz", "integrity": "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw="}, "simple-swizzle": {"version": "0.2.2", "resolved": "http://registry.npm.baidu-int.com/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "requires": {"is-arrayish": "^0.3.1"}, "dependencies": {"is-arrayish": {"version": "0.3.2", "resolved": "http://registry.npm.baidu-int.com/is-arrayish/-/is-arrayish-0.3.2.tgz", "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="}}}, "slash": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/slash/-/slash-2.0.0.tgz", "integrity": "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q="}, "slice-ansi": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/slice-ansi/-/slice-ansi-2.1.0.tgz", "integrity": "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=", "dev": true, "requires": {"ansi-styles": "^3.2.0", "astral-regex": "^1.0.0", "is-fullwidth-code-point": "^2.0.0"}}, "snapdragon": {"version": "0.8.2", "resolved": "http://registry.npm.baidu-int.com/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://registry.npm.baidu-int.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "sntp": {"version": "1.0.9", "resolved": "http://registry.npm.baidu-int.com/sntp/-/sntp-1.0.9.tgz", "integrity": "sha1-ZUEYTMkK7qbG57NeJlkIJEPGYZg=", "optional": true, "requires": {"hoek": "2.x.x"}}, "sockjs": {"version": "0.3.20", "resolved": "http://registry.npm.baidu-int.com/sockjs/-/sockjs-0.3.20.tgz", "integrity": "sha1-smooPsVi74smh7RAM6Tuzqx12FU=", "requires": {"faye-websocket": "^0.10.0", "uuid": "^3.4.0", "websocket-driver": "0.6.5"}}, "sockjs-client": {"version": "1.4.0", "resolved": "http://registry.npm.baidu-int.com/sockjs-client/-/sockjs-client-1.4.0.tgz", "integrity": "sha512-5zaLyO8/nri5cua0VtOrFXBPK1jbL4+1cebT/mmKA1E1ZXOvJrII75bPu0l0k843G/+iAbhEqzyKr0w/eCCj7g==", "requires": {"debug": "^3.2.5", "eventsource": "^1.0.7", "faye-websocket": "~0.11.1", "inherits": "^2.0.3", "json3": "^3.3.2", "url-parse": "^1.4.3"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://registry.npm.baidu-int.com/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}, "faye-websocket": {"version": "0.11.3", "resolved": "http://registry.npm.baidu-int.com/faye-websocket/-/faye-websocket-0.11.3.tgz", "integrity": "sha512-D2y4bovYpzziGgbHYtGCMjlJM36vAl/y+xUyn1C+FVx8szd1E+86KwVw6XvYSzOP8iMpm1X0I4xJD+QtUb36OA==", "requires": {"websocket-driver": ">=0.5.1"}}}}, "sort-keys": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/sort-keys/-/sort-keys-1.1.2.tgz", "integrity": "sha1-RBttTTRnmPG05J6JIK37oOVD+a0=", "requires": {"is-plain-obj": "^1.0.0"}}, "sort-keys-length": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/sort-keys-length/-/sort-keys-length-1.0.1.tgz", "integrity": "sha1-nLb09OnkgVWmqgZx7dM2/xR5oYg=", "requires": {"sort-keys": "^1.0.0"}}, "source-list-map": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/source-list-map/-/source-list-map-2.0.1.tgz", "integrity": "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="}, "source-map": {"version": "0.5.7", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}, "source-map-resolve": {"version": "0.5.3", "resolved": "http://registry.npm.baidu-int.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "integrity": "sha1-GQhmvs51U+H48mei7oLGBrVQmho=", "requires": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.19", "resolved": "http://registry.npm.baidu-int.com/source-map-support/-/source-map-support-0.5.19.tgz", "integrity": "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=", "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "source-map-url": {"version": "0.4.0", "resolved": "http://registry.npm.baidu-int.com/source-map-url/-/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="}, "spdx-correct": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/spdx-correct/-/spdx-correct-3.1.1.tgz", "integrity": "sha1-3s6BrJweZxPl99G28X1Gj6U9iak=", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "resolved": "http://registry.npm.baidu-int.com/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz", "integrity": "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "http://registry.npm.baidu-int.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.5", "resolved": "http://registry.npm.baidu-int.com/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz", "integrity": "sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q==", "dev": true}, "spdy": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/spdy/-/spdy-4.0.2.tgz", "integrity": "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=", "requires": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}}, "spdy-transport": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/spdy-transport/-/spdy-transport-3.0.0.tgz", "integrity": "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=", "requires": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://registry.npm.baidu-int.com/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}}}, "split-string": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/split-string/-/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "requires": {"extend-shallow": "^3.0.0"}}, "sprintf-js": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "sshpk": {"version": "1.16.1", "resolved": "http://registry.npm.baidu-int.com/sshpk/-/sshpk-1.16.1.tgz", "integrity": "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=", "optional": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "ssri": {"version": "6.0.1", "resolved": "http://registry.npm.baidu-int.com/ssri/-/ssri-6.0.1.tgz", "integrity": "sha512-3Wge10hNcT1Kur4PDFwEieXSCMCJs/7WvSACcrMYrNp+b8kDL1/0wJch5Ni2WrtwEa2IO8OsVfeKIciKCDx/QA==", "requires": {"figgy-pudding": "^3.5.1"}}, "stable": {"version": "0.1.8", "resolved": "http://registry.npm.baidu-int.com/stable/-/stable-0.1.8.tgz", "integrity": "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="}, "static-extend": {"version": "0.1.2", "resolved": "http://registry.npm.baidu-int.com/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://registry.npm.baidu-int.com/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "statuses": {"version": "1.5.0", "resolved": "http://registry.npm.baidu-int.com/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "stream-array": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/stream-array/-/stream-array-1.1.2.tgz", "integrity": "sha1-nl9zRfITfDDuO0mLkRToC1K7frU=", "requires": {"readable-stream": "~2.1.0"}, "dependencies": {"process-nextick-args": {"version": "1.0.7", "resolved": "http://registry.npm.baidu-int.com/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M="}, "readable-stream": {"version": "2.1.5", "resolved": "http://registry.npm.baidu-int.com/readable-stream/-/readable-stream-2.1.5.tgz", "integrity": "sha1-ZvqLcg4UOLNkaB8q0aY8YYRIydA=", "requires": {"buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://registry.npm.baidu-int.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "stream-browserify": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/stream-browserify/-/stream-browserify-2.0.2.tgz", "integrity": "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=", "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-each": {"version": "1.2.3", "resolved": "http://registry.npm.baidu-int.com/stream-each/-/stream-each-1.2.3.tgz", "integrity": "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==", "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-http": {"version": "2.8.3", "resolved": "http://registry.npm.baidu-int.com/stream-http/-/stream-http-2.8.3.tgz", "integrity": "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==", "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "stream-shift": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/stream-shift/-/stream-shift-1.0.1.tgz", "integrity": "sha1-1wiCgVWasneEJCebCHfaPDktWj0="}, "strict-uri-encode": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz", "integrity": "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="}, "string-width": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/string-width/-/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="}, "strip-ansi": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "requires": {"ansi-regex": "^4.1.0"}}}}, "string.prototype.trimend": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/string.prototype.trimend/-/string.prototype.trimend-1.0.1.tgz", "integrity": "sha1-hYEqa4R6wAInD1gIFGBkyZX7aRM=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "string.prototype.trimstart": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.1.tgz", "integrity": "sha1-FK9tnzSwU/fPyJty+PLuFLkDmlQ=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}, "stringstream": {"version": "0.0.6", "resolved": "http://registry.npm.baidu-int.com/stringstream/-/stringstream-0.0.6.tgz", "integrity": "sha512-87GEBAkegbBcweToUrdzf3eLhWNg06FJTebl4BVJz/JgWy8CvEr9dRtX5qWphiynMSQlxxi+QqN0z5T32SLlhA==", "optional": true}, "strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="}, "strip-dirs": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/strip-dirs/-/strip-dirs-2.1.0.tgz", "integrity": "sha1-SYdzYmT8NEzyD2w0rKnRPR1O1sU=", "requires": {"is-natural-number": "^4.0.1"}}, "strip-eof": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/strip-eof/-/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="}, "strip-json-comments": {"version": "2.0.1", "resolved": "http://registry.npm.baidu-int.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true}, "strip-outer": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/strip-outer/-/strip-outer-1.0.1.tgz", "integrity": "sha1-sv0qv2YEudHmATBXGV34Nrip1jE=", "requires": {"escape-string-regexp": "^1.0.2"}}, "style-loader": {"version": "0.23.1", "resolved": "http://registry.npm.baidu-int.com/style-loader/-/style-loader-0.23.1.tgz", "integrity": "sha512-XK+uv9kWwhZMZ1y7mysB+zoihsEj4wneFWAS5qoiLwzW0WzSqMrrsIy+a3zkQJq0ipFtBpX5W3MqyRIBF/WFGg==", "requires": {"loader-utils": "^1.1.0", "schema-utils": "^1.0.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "stylehacks": {"version": "4.0.3", "resolved": "http://registry.npm.baidu-int.com/stylehacks/-/stylehacks-4.0.3.tgz", "integrity": "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=", "requires": {"browserslist": "^4.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0"}, "dependencies": {"postcss-selector-parser": {"version": "3.1.2", "resolved": "http://registry.npm.baidu-int.com/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "integrity": "sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=", "requires": {"dot-prop": "^5.2.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}}}, "supports-color": {"version": "5.5.0", "resolved": "http://registry.npm.baidu-int.com/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "requires": {"has-flag": "^3.0.0"}}, "supports-hyperlinks": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/supports-hyperlinks/-/supports-hyperlinks-1.0.1.tgz", "integrity": "sha1-cdrt82zBBgrFEAw1G7PaSMKcDvc=", "dev": true, "requires": {"has-flag": "^2.0.0", "supports-color": "^5.0.0"}, "dependencies": {"has-flag": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/has-flag/-/has-flag-2.0.0.tgz", "integrity": "sha1-6CB68cx7MNRGzHC3NLXovhj4jVE=", "dev": true}}}, "svgo": {"version": "1.3.2", "resolved": "http://registry.npm.baidu-int.com/svgo/-/svgo-1.3.2.tgz", "integrity": "sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==", "requires": {"chalk": "^2.4.1", "coa": "^2.0.2", "css-select": "^2.0.0", "css-select-base-adapter": "^0.1.1", "css-tree": "1.0.0-alpha.37", "csso": "^4.0.2", "js-yaml": "^3.13.1", "mkdirp": "~0.5.1", "object.values": "^1.1.0", "sax": "~1.2.4", "stable": "^0.1.8", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}}, "table": {"version": "5.4.6", "resolved": "http://registry.npm.baidu-int.com/table/-/table-5.4.6.tgz", "integrity": "sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==", "dev": true, "requires": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}}, "tapable": {"version": "1.1.3", "resolved": "http://registry.npm.baidu-int.com/tapable/-/tapable-1.1.3.tgz", "integrity": "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA=="}, "tar-stream": {"version": "1.6.2", "resolved": "http://registry.npm.baidu-int.com/tar-stream/-/tar-stream-1.6.2.tgz", "integrity": "sha512-rzS0heiNf8Xn7/mpdSVVSMAWAoy9bfb1WOTYC78Z0UQKeKa/CWS8FOq0lKGNa8DWKAn9gxjCvMLYc5PGXYlK2A==", "requires": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}}, "teamcity-service-messages": {"version": "0.1.11", "resolved": "http://registry.npm.baidu-int.com/teamcity-service-messages/-/teamcity-service-messages-0.1.11.tgz", "integrity": "sha1-tVEGd8KoXgCG3lwlGrOrL3NEJd8="}, "terser": {"version": "4.8.0", "resolved": "http://registry.npm.baidu-int.com/terser/-/terser-4.8.0.tgz", "integrity": "sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=", "requires": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "terser-webpack-plugin": {"version": "1.4.4", "resolved": "http://registry.npm.baidu-int.com/terser-webpack-plugin/-/terser-webpack-plugin-1.4.4.tgz", "integrity": "sha1-LGNUQ0cyS6r6mla6rd8WNMir/C8=", "requires": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^3.1.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "serialize-javascript": {"version": "3.1.0", "resolved": "http://registry.npm.baidu-int.com/serialize-javascript/-/serialize-javascript-3.1.0.tgz", "integrity": "sha1-i/OpFwcSZk7yVhtEtpHq/jmSFOo=", "requires": {"randombytes": "^2.1.0"}}, "source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "text-table": {"version": "0.2.0", "resolved": "http://registry.npm.baidu-int.com/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "through": {"version": "2.3.8", "resolved": "http://registry.npm.baidu-int.com/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "through2": {"version": "2.0.5", "resolved": "http://registry.npm.baidu-int.com/through2/-/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "requires": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "thunky": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/thunky/-/thunky-1.1.0.tgz", "integrity": "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="}, "timed-out": {"version": "4.0.1", "resolved": "http://registry.npm.baidu-int.com/timed-out/-/timed-out-4.0.1.tgz", "integrity": "sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8="}, "timers-browserify": {"version": "2.0.11", "resolved": "http://registry.npm.baidu-int.com/timers-browserify/-/timers-browserify-2.0.11.tgz", "integrity": "sha512-60aV6sgJ5YEbzUdn9c8kYGIqOubPoUdqQCul3SBAsRCZ40s6Y5cMcrW4dt3/k/EsbLVJNl9n6Vz3fTc+k2GeKQ==", "requires": {"setimmediate": "^1.0.4"}}, "timsort": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/timsort/-/timsort-0.3.0.tgz", "integrity": "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="}, "tmp": {"version": "0.0.33", "resolved": "http://registry.npm.baidu-int.com/tmp/-/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "requires": {"os-tmpdir": "~1.0.2"}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M="}, "to-buffer": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/to-buffer/-/to-buffer-1.1.1.tgz", "integrity": "sha512-lx9B5iv7msuFYE3dytT+KE5tap+rNYw+K4jVkb9R/asAb+pbBSM17jtunHplhBe6RRJdZx3Pn2Jph24O32mOVg=="}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="}, "to-object-path": {"version": "0.3.0", "resolved": "http://registry.npm.baidu-int.com/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.baidu-int.com/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://registry.npm.baidu-int.com/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "toidentifier": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw=="}, "traverse": {"version": "0.3.9", "resolved": "http://registry.npm.baidu-int.com/traverse/-/traverse-0.3.9.tgz", "integrity": "sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk="}, "trim-repeated": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/trim-repeated/-/trim-repeated-1.0.0.tgz", "integrity": "sha1-42RqLqTokTEr9+rObPsFOAvAHCE=", "requires": {"escape-string-regexp": "^1.0.2"}}, "ts-pnp": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/ts-pnp/-/ts-pnp-1.2.0.tgz", "integrity": "sha1-pQCtCEsHmPHDBxrzkeZZEshrypI="}, "tsconfig-paths": {"version": "3.9.0", "resolved": "http://registry.npm.baidu-int.com/tsconfig-paths/-/tsconfig-paths-3.9.0.tgz", "integrity": "sha512-dRcuzokWhajtZWkQsDVKbWyY+jgcLC5sqJhg2PSgf4ZkH2aHPvaOY8YWGhmjb68b5qqTfasSsDO9k7RUiEmZAw==", "requires": {"@types/json5": "^0.0.29", "json5": "^1.0.1", "minimist": "^1.2.0", "strip-bom": "^3.0.0"}, "dependencies": {"json5": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/json5/-/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "requires": {"minimist": "^1.2.0"}}}}, "tsconfig-paths-webpack-plugin": {"version": "3.2.0", "resolved": "http://registry.npm.baidu-int.com/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-3.2.0.tgz", "integrity": "sha512-S/gOOPOkV8rIL4LurZ1vUdYCVgo15iX9ZMJ6wx6w2OgcpT/G4wMyHB6WM+xheSqGMrWKuxFul+aXpCju3wmj/g==", "requires": {"chalk": "^2.3.0", "enhanced-resolve": "^4.0.0", "tsconfig-paths": "^3.4.0"}}, "tslib": {"version": "1.13.0", "resolved": "http://registry.npm.baidu-int.com/tslib/-/tslib-1.13.0.tgz", "integrity": "sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM="}, "tty-browserify": {"version": "0.0.0", "resolved": "http://registry.npm.baidu-int.com/tty-browserify/-/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY="}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://registry.npm.baidu-int.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://registry.npm.baidu-int.com/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "optional": true}, "type-check": {"version": "0.3.2", "resolved": "http://registry.npm.baidu-int.com/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "requires": {"prelude-ls": "~1.1.2"}}, "type-fest": {"version": "0.11.0", "resolved": "http://registry.npm.baidu-int.com/type-fest/-/type-fest-0.11.0.tgz", "integrity": "sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E="}, "type-is": {"version": "1.6.18", "resolved": "http://registry.npm.baidu-int.com/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typedarray": {"version": "0.0.6", "resolved": "http://registry.npm.baidu-int.com/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "uglify-js": {"version": "3.10.0", "resolved": "http://registry.npm.baidu-int.com/uglify-js/-/uglify-js-3.10.0.tgz", "integrity": "sha1-OXp+bjHOggv9HLVbgE7hQMWHqec="}, "uglifyjs-webpack-plugin": {"version": "2.2.0", "resolved": "http://registry.npm.baidu-int.com/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-2.2.0.tgz", "integrity": "sha512-mHSkufBmBuJ+KHQhv5H0MXijtsoA1lynJt1lXOaotja8/I0pR4L9oGaPIZw+bQBOFittXZg9OC1sXSGO9D9ZYg==", "requires": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^1.7.0", "source-map": "^0.6.1", "uglify-js": "^3.6.0", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "unbzip2-stream": {"version": "1.4.3", "resolved": "http://registry.npm.baidu-int.com/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz", "integrity": "sha1-sNoExDcTEd93HNwhXofyEwmRrOc=", "requires": {"buffer": "^5.2.1", "through": "^2.3.8"}}, "unicode-canonical-property-names-ecmascript": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "integrity": "sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ=="}, "unicode-match-property-ecmascript": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz", "integrity": "sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg==", "requires": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}}, "unicode-match-property-value-ecmascript": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.2.0.tgz", "integrity": "sha1-DZH2AO7rMJaqlisdb8iIduZOpTE="}, "unicode-property-aliases-ecmascript": {"version": "1.1.0", "resolved": "http://registry.npm.baidu-int.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.1.0.tgz", "integrity": "sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ="}, "union-value": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/union-value/-/union-value-1.0.1.tgz", "integrity": "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "uniq": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/uniq/-/uniq-1.0.1.tgz", "integrity": "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8="}, "uniqs": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/uniqs/-/uniqs-2.0.0.tgz", "integrity": "sha1-/+3ks2slKQaW5uFl1KWe25mOawI="}, "unique-filename": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==", "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.2", "resolved": "http://registry.npm.baidu-int.com/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==", "requires": {"imurmurhash": "^0.1.4"}}, "unpipe": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "unquote": {"version": "1.1.1", "resolved": "http://registry.npm.baidu-int.com/unquote/-/unquote-1.1.1.tgz", "integrity": "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ="}, "unset-value": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://registry.npm.baidu-int.com/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://registry.npm.baidu-int.com/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://registry.npm.baidu-int.com/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}}}, "unzipper": {"version": "0.10.11", "resolved": "http://registry.npm.baidu-int.com/unzipper/-/unzipper-0.10.11.tgz", "integrity": "sha1-C0mRRGRyy9uS7nQDkJ8mwkGceC4=", "requires": {"big-integer": "^1.6.17", "binary": "~0.3.0", "bluebird": "~3.4.1", "buffer-indexof-polyfill": "~1.0.0", "duplexer2": "~0.1.4", "fstream": "^1.0.12", "graceful-fs": "^4.2.2", "listenercount": "~1.0.1", "readable-stream": "~2.3.6", "setimmediate": "~1.0.4"}, "dependencies": {"bluebird": {"version": "3.4.7", "resolved": "http://registry.npm.baidu-int.com/bluebird/-/bluebird-3.4.7.tgz", "integrity": "sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM="}}}, "upath": {"version": "1.2.0", "resolved": "http://registry.npm.baidu-int.com/upath/-/upath-1.2.0.tgz", "integrity": "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="}, "uri-js": {"version": "4.2.2", "resolved": "http://registry.npm.baidu-int.com/uri-js/-/uri-js-4.2.2.tgz", "integrity": "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==", "requires": {"punycode": "^2.1.0"}}, "urix": {"version": "0.1.0", "resolved": "http://registry.npm.baidu-int.com/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="}, "url": {"version": "0.11.0", "resolved": "http://registry.npm.baidu-int.com/url/-/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "http://registry.npm.baidu-int.com/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="}}}, "url-loader": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/url-loader/-/url-loader-1.1.2.tgz", "integrity": "sha512-dXHkKmw8FhPqu8asTc1puBfe3TehOCo2+RmOOev5suNCIYBcT626kxiWg1NBVkwc4rO8BGa7gP70W7VXuqHrjg==", "requires": {"loader-utils": "^1.1.0", "mime": "^2.0.3", "schema-utils": "^1.0.0"}, "dependencies": {"mime": {"version": "2.4.6", "resolved": "http://registry.npm.baidu-int.com/mime/-/mime-2.4.6.tgz", "integrity": "sha1-5bQHyQ20QvK+tbFiNz0Htpr/pNE="}, "schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "url-parse": {"version": "1.4.7", "resolved": "http://registry.npm.baidu-int.com/url-parse/-/url-parse-1.4.7.tgz", "integrity": "sha1-qKg1NejACjFuQDpdtKwbm4U64ng=", "requires": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "url-parse-lax": {"version": "3.0.0", "resolved": "http://registry.npm.baidu-int.com/url-parse-lax/-/url-parse-lax-3.0.0.tgz", "integrity": "sha1-FrXK/Afb42dsGxmZF3gj1lA6yww=", "requires": {"prepend-http": "^2.0.0"}}, "url-to-options": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/url-to-options/-/url-to-options-1.0.1.tgz", "integrity": "sha1-FQWgOiiaSMvXpDTvuu7FBV9WM6k="}, "use": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/use/-/use-3.1.1.tgz", "integrity": "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="}, "util": {"version": "0.11.1", "resolved": "http://registry.npm.baidu-int.com/util/-/util-0.11.1.tgz", "integrity": "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==", "requires": {"inherits": "2.0.3"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "http://registry.npm.baidu-int.com/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "util.promisify": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/util.promisify/-/util.promisify-1.0.1.tgz", "integrity": "sha1-a693dLgO6w91INi4HQeYKlmruu4=", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.2", "has-symbols": "^1.0.1", "object.getownpropertydescriptors": "^2.1.0"}}, "utila": {"version": "0.4.0", "resolved": "http://registry.npm.baidu-int.com/utila/-/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="}, "utils-merge": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "uuid": {"version": "3.4.0", "resolved": "http://registry.npm.baidu-int.com/uuid/-/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="}, "v8-compile-cache": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/v8-compile-cache/-/v8-compile-cache-2.1.1.tgz", "integrity": "sha1-VLw83UMxe8qR413K8wWxpyN950U="}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "http://registry.npm.baidu-int.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "integrity": "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "vary": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "vendors": {"version": "1.0.4", "resolved": "http://registry.npm.baidu-int.com/vendors/-/vendors-1.0.4.tgz", "integrity": "sha1-4rgApT56Kbk1BsPPQRANFsTErY4="}, "verror": {"version": "1.10.0", "resolved": "http://registry.npm.baidu-int.com/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "optional": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "vm-browserify": {"version": "1.1.2", "resolved": "http://registry.npm.baidu-int.com/vm-browserify/-/vm-browserify-1.1.2.tgz", "integrity": "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ=="}, "watchpack": {"version": "1.7.2", "resolved": "http://registry.npm.baidu-int.com/watchpack/-/watchpack-1.7.2.tgz", "integrity": "sha1-wC5NTUmRPD5+EiwzJTZa+dMx6ao=", "requires": {"chokidar": "^3.4.0", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0", "watchpack-chokidar2": "^2.0.0"}}, "watchpack-chokidar2": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/watchpack-chokidar2/-/watchpack-chokidar2-2.0.0.tgz", "integrity": "sha1-mUihhmy71suCTeoTp+1pH2yN3/A=", "optional": true, "requires": {"chokidar": "^2.1.8"}, "dependencies": {"anymatch": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "optional": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "optional": true, "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "binary-extensions": {"version": "1.13.1", "resolved": "http://registry.npm.baidu-int.com/binary-extensions/-/binary-extensions-1.13.1.tgz", "integrity": "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=", "optional": true}, "chokidar": {"version": "2.1.8", "resolved": "http://registry.npm.baidu-int.com/chokidar/-/chokidar-2.1.8.tgz", "integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==", "optional": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "fsevents": {"version": "1.2.13", "resolved": "http://registry.npm.baidu-int.com/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "optional": true, "requires": {"bindings": "^1.5.0", "nan": "^2.12.1"}}, "is-binary-path": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "optional": true, "requires": {"binary-extensions": "^1.0.0"}}, "readdirp": {"version": "2.2.1", "resolved": "http://registry.npm.baidu-int.com/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "optional": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}}}, "wbuf": {"version": "1.7.3", "resolved": "http://registry.npm.baidu-int.com/wbuf/-/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "requires": {"minimalistic-assert": "^1.0.0"}}, "webpack": {"version": "4.43.0", "resolved": "http://registry.npm.baidu-int.com/webpack/-/webpack-4.43.0.tgz", "integrity": "sha1-xIVHsR1WMiTFYdrRFyyKoLimeOY=", "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.1.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.6.1", "webpack-sources": "^1.4.1"}, "dependencies": {"acorn": {"version": "6.4.1", "resolved": "http://registry.npm.baidu-int.com/acorn/-/acorn-6.4.1.tgz", "integrity": "sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ="}, "schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "webpack-cli": {"version": "3.3.12", "resolved": "http://registry.npm.baidu-int.com/webpack-cli/-/webpack-cli-3.3.12.tgz", "integrity": "sha1-lOmtoIFFPNCqYJyZ5QABL9OtLUo=", "requires": {"chalk": "^2.4.2", "cross-spawn": "^6.0.5", "enhanced-resolve": "^4.1.1", "findup-sync": "^3.0.0", "global-modules": "^2.0.0", "import-local": "^2.0.0", "interpret": "^1.4.0", "loader-utils": "^1.4.0", "supports-color": "^6.1.0", "v8-compile-cache": "^2.1.1", "yargs": "^13.3.2"}, "dependencies": {"supports-color": {"version": "6.1.0", "resolved": "http://registry.npm.baidu-int.com/supports-color/-/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "requires": {"has-flag": "^3.0.0"}}}}, "webpack-dev-middleware": {"version": "3.7.2", "resolved": "http://registry.npm.baidu-int.com/webpack-dev-middleware/-/webpack-dev-middleware-3.7.2.tgz", "integrity": "sha512-1xC42LxbYoqLNAhV6YzTYacicgMZQTqRd27Sim9wn5hJrX3I5nxYy1SxSd4+gjUFsz1dQFj+yEe6zEVmSkeJjw==", "requires": {"memory-fs": "^0.4.1", "mime": "^2.4.4", "mkdirp": "^0.5.1", "range-parser": "^1.2.1", "webpack-log": "^2.0.0"}, "dependencies": {"mime": {"version": "2.4.6", "resolved": "http://registry.npm.baidu-int.com/mime/-/mime-2.4.6.tgz", "integrity": "sha1-5bQHyQ20QvK+tbFiNz0Htpr/pNE="}}}, "webpack-dev-server": {"version": "3.11.0", "resolved": "http://registry.npm.baidu-int.com/webpack-dev-server/-/webpack-dev-server-3.11.0.tgz", "integrity": "sha1-jxVKO84bz9HMYY705wMniFXn/4w=", "requires": {"ansi-html": "0.0.7", "bonjour": "^3.5.0", "chokidar": "^2.1.8", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "debug": "^4.1.1", "del": "^4.1.1", "express": "^4.17.1", "html-entities": "^1.3.1", "http-proxy-middleware": "0.19.1", "import-local": "^2.0.0", "internal-ip": "^4.3.0", "ip": "^1.1.5", "is-absolute-url": "^3.0.3", "killable": "^1.0.1", "loglevel": "^1.6.8", "opn": "^5.5.0", "p-retry": "^3.0.1", "portfinder": "^1.0.26", "schema-utils": "^1.0.0", "selfsigned": "^1.10.7", "semver": "^6.3.0", "serve-index": "^1.9.1", "sockjs": "0.3.20", "sockjs-client": "1.4.0", "spdy": "^4.0.2", "strip-ansi": "^3.0.1", "supports-color": "^6.1.0", "url": "^0.11.0", "webpack-dev-middleware": "^3.7.2", "webpack-log": "^2.0.0", "ws": "^6.2.1", "yargs": "^13.3.2"}, "dependencies": {"anymatch": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "http://registry.npm.baidu-int.com/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "binary-extensions": {"version": "1.13.1", "resolved": "http://registry.npm.baidu-int.com/binary-extensions/-/binary-extensions-1.13.1.tgz", "integrity": "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U="}, "chokidar": {"version": "2.1.8", "resolved": "http://registry.npm.baidu-int.com/chokidar/-/chokidar-2.1.8.tgz", "integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==", "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "fsevents": {"version": "1.2.13", "resolved": "http://registry.npm.baidu-int.com/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "optional": true, "requires": {"bindings": "^1.5.0", "nan": "^2.12.1"}}, "is-absolute-url": {"version": "3.0.3", "resolved": "http://registry.npm.baidu-int.com/is-absolute-url/-/is-absolute-url-3.0.3.tgz", "integrity": "sha512-opmNIX7uFnS96NtPmhWQgQx6/NYFgsUXYMllcfzwWKUMwfo8kku1TvE6hkNcH+Q1ts5cMVrsY7j0bxXQDciu9Q=="}, "is-binary-path": {"version": "1.0.1", "resolved": "http://registry.npm.baidu-int.com/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "requires": {"binary-extensions": "^1.0.0"}}, "opn": {"version": "5.5.0", "resolved": "http://registry.npm.baidu-int.com/opn/-/opn-5.5.0.tgz", "integrity": "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=", "requires": {"is-wsl": "^1.1.0"}}, "readdirp": {"version": "2.2.1", "resolved": "http://registry.npm.baidu-int.com/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}, "schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "semver": {"version": "6.3.0", "resolved": "http://registry.npm.baidu-int.com/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="}, "supports-color": {"version": "6.1.0", "resolved": "http://registry.npm.baidu-int.com/supports-color/-/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "requires": {"has-flag": "^3.0.0"}}}}, "webpack-log": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/webpack-log/-/webpack-log-2.0.0.tgz", "integrity": "sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg==", "requires": {"ansi-colors": "^3.0.0", "uuid": "^3.3.2"}}, "webpack-merge": {"version": "4.2.2", "resolved": "http://registry.npm.baidu-int.com/webpack-merge/-/webpack-merge-4.2.2.tgz", "integrity": "sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==", "requires": {"lodash": "^4.17.15"}}, "webpack-sources": {"version": "1.4.3", "resolved": "http://registry.npm.baidu-int.com/webpack-sources/-/webpack-sources-1.4.3.tgz", "integrity": "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==", "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://registry.npm.baidu-int.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "websocket-driver": {"version": "0.6.5", "resolved": "http://registry.npm.baidu-int.com/websocket-driver/-/websocket-driver-0.6.5.tgz", "integrity": "sha1-XLJVbOuF9Dc8bYI4qmkchFThOjY=", "requires": {"websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4", "resolved": "http://registry.npm.baidu-int.com/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "integrity": "sha1-f4RzvIOd/YdgituV1+sHUhFXikI="}, "which": {"version": "1.3.1", "resolved": "http://registry.npm.baidu-int.com/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "http://registry.npm.baidu-int.com/which-module/-/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="}, "word-wrap": {"version": "1.2.3", "resolved": "http://registry.npm.baidu-int.com/word-wrap/-/word-wrap-1.2.3.tgz", "integrity": "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=", "dev": true}, "wordwrap": {"version": "1.0.0", "resolved": "http://registry.npm.baidu-int.com/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus="}, "worker-farm": {"version": "1.7.0", "resolved": "http://registry.npm.baidu-int.com/worker-farm/-/worker-farm-1.7.0.tgz", "integrity": "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=", "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "5.1.0", "resolved": "http://registry.npm.baidu-int.com/wrap-ansi/-/wrap-ansi-5.1.0.tgz", "integrity": "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=", "requires": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "http://registry.npm.baidu-int.com/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="}, "strip-ansi": {"version": "5.2.0", "resolved": "http://registry.npm.baidu-int.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "requires": {"ansi-regex": "^4.1.0"}}}}, "wrappy": {"version": "1.0.2", "resolved": "http://registry.npm.baidu-int.com/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write": {"version": "1.0.3", "resolved": "http://registry.npm.baidu-int.com/write/-/write-1.0.3.tgz", "integrity": "sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=", "dev": true, "requires": {"mkdirp": "^0.5.1"}}, "ws": {"version": "6.2.1", "resolved": "http://registry.npm.baidu-int.com/ws/-/ws-6.2.1.tgz", "integrity": "sha1-RC/fCkftZPWbal2P8TD0dI7VJPs=", "requires": {"async-limiter": "~1.0.0"}}, "xtend": {"version": "4.0.2", "resolved": "http://registry.npm.baidu-int.com/xtend/-/xtend-4.0.2.tgz", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="}, "y18n": {"version": "4.0.0", "resolved": "http://registry.npm.baidu-int.com/y18n/-/y18n-4.0.0.tgz", "integrity": "sha1-le+U+F7MgdAHwmThkKEg8KPIVms="}, "yallist": {"version": "3.1.1", "resolved": "http://registry.npm.baidu-int.com/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="}, "yargs": {"version": "13.3.2", "resolved": "http://registry.npm.baidu-int.com/yargs/-/yargs-13.3.2.tgz", "integrity": "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=", "requires": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.2"}}, "yargs-parser": {"version": "13.1.2", "resolved": "http://registry.npm.baidu-int.com/yargs-parser/-/yargs-parser-13.1.2.tgz", "integrity": "sha1-Ew8JcC667vJlDVTObj5XBvek+zg=", "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "yauzl": {"version": "2.10.0", "resolved": "http://registry.npm.baidu-int.com/yauzl/-/yauzl-2.10.0.tgz", "integrity": "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=", "requires": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}}}