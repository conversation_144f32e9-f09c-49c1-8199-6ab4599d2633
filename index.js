#!/usr/bin/env node
/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * @file index.js
 * <AUTHOR>
 */
const program = require('commander');
const {greenBright} = require('chalk');

const logger = require('./logger');
const {version} = require('./package.json');

const DevProvider = require('./lib/providers/dev-provider');

const PkgProvider = require('./lib/providers/pkg-provider');

const FlagProvider = require('./lib/providers/flag-provider');
const AutoUpdater = require('./lib/providers/update-provider');
const BuildProvider = require('./lib/providers/build-provider');
const InitProvider = require('./lib/providers/init-provider');

AutoUpdater.asyncCheckVersion();

program
    .version(greenBright(`BaiduCloud FE CLI v${version}`), '-v, --version');

program.command('dev')
    .option('-P, --port <port>', 'DevServer 端口', 8889)
    .option('-C, --config <file>', '项目配置文件', `${process.cwd()}/bce-config.js`)
    .option('--template [template]', '项目模板')
    .option('--templateId [templateId]', '项目id')
    .option('--sdk', '工具库', false)
    .option('--no-i18n', '国际化编译', false)
    .option('--mock', 'mock模式', false)
    .action(DevProvider.action);

program.command('build')
    .option('-D, --dir [dir]', 'the project root path', process.cwd())
    .option('-C, --config <file>', '项目配置文件', `${process.cwd()}/bce-config.js`)
    .option('--template [template]', '项目模板')
    .option('--templateId [templateId]', '项目id')
    .option('--sdk', '工具库', false)
    .option('--no-i18n', '国际化编译&提取', false)
    .action(BuildProvider.action);

program.command('init')
    .action(InitProvider.action);

program.command('list')
    .option('-C, --config <file>', '项目配置文件', `${process.cwd()}/bce-config.js`)
    .action(PkgProvider.list);

program.command('search <name>')
    .action(PkgProvider.search);

program.command('flag')
    .option('-C, --config <file>', '项目配置文件', `${process.cwd()}/bce-config.js`)
    .option('--gentypes', '生成本地开关types', false)
    .action(FlagProvider.aciton);

program.on('command:*', () => {
    logger.error(
        'Invalid command: %s \nSee --help for a list of available commands.',
        program.args.join(' ')
    );
    process.exit(1);
});

program.parse(process.argv);

if (program.args.length === 0) {
    program.help();
    greenBright('更多详细信息，请查看文档：http://bce.console.baidu-int.com/bce-doc/book/view/26e80d90-b050-4a7e-9470-697247f82698');
}
