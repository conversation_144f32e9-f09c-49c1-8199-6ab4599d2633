/*
 * @Date: 2021-01-15 11:19:08
 * @LastEditTime: 2021-01-18 11:27:10
 * @FilePath: /newlss/.eslintrc.js
 * @Author: <EMAIL>
 */

module.exports = {
    extends: ['@ecomfe/eslint-config', '@ecomfe/eslint-config/import', '@ecomfe/eslint-config/typescript'],
    settings: {
        'import/resolver': {
            // typescript: {
            //     alwaysTryTypes: true
            // },
            node: {
                extensions: ['.js', '.jsx', '.ts', '.tsx']
            }
        }
    },
    rules: {
        'comma-dangle': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-empty-function': 'off',
        '@typescript-eslint/no-this-alias': 'off',
        '@typescript-eslint/restrict-plus-operands': 'off',
        'import/no-unresolved': 'off',
        'no-loop-func': 'off',
        'guard-for-in': 'off',
        'max-len': 'off'
    }
};
