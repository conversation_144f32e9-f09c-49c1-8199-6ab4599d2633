{"name": "@baidu/bce-cli", "version": "1.1.7-beta.2", "description": "BaiduCloud Console-FE CLI", "main": "index.js", "scripts": {"lint": "eslint --cache --format=node_modules/eslint-formatter-pretty src"}, "bin": {"bce-cli": "index.js"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/baiducloud/fe-cli"}, "author": "zhanghao25", "license": "MIT", "dependencies": {"@babel/core": "^7.9.6", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-decorators": "^7.7.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-typescript": "^7.12.1", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.9.0", "@baiducloud/i18n": "^1.0.0-rc.29", "acorn": "^7.3.1", "acorn-walk": "^7.2.0", "add-asset-html-webpack-plugin": "^3.1.3", "autoprefixer": "^9.8.0", "babel-loader": "^8.1.0", "chalk": "^2.4.2", "commander": "^2.20.3", "core-js": "^3.6.5", "css-loader": "^2.1.1", "cssnano": "^4.1.10", "debug": "^4.1.1", "dependency-cruiser": "9.9.2", "download": "^7.1.0", "html-webpack-plugin": "^4.3.0", "inquirer": "^7.3.3", "less": "^2.7.3", "less-loader": "^4.1.0", "less-plugin-functions": "^1.0.0", "log-symbols": "^3.0.0", "mini-css-extract-plugin": "^0.5.0", "mkdir-recursive": "^0.4.0", "ncp": "^2.0.0", "netutil": "0.0.2", "opn": "^6.0.0", "path-to-regexp": "^6.1.0", "postcss-loader": "^3.0.0", "script-loader": "^0.7.2", "shelljs": "^0.8.4", "stream-array": "^1.1.2", "style-loader": "^0.23.1", "uglifyjs-webpack-plugin": "^2.2.0", "unzipper": "^0.10.11", "url": "^0.11.0", "url-loader": "^1.1.2", "webpack": "^4.43.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.11.0", "webpack-merge": "^4.2.2", "webpack-sources": "^1.4.3"}, "devDependencies": {"ajv": "^6.12.2", "babel-eslint": "8.0.1", "core-js-bundle": "^3.6.5", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^13.2.0", "eslint-formatter-pretty": "^2.1.1", "eslint-plugin-import": "^2.20.2"}}