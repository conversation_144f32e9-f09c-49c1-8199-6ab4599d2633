/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.base.config.js
 * <AUTHOR>
 */

const path = require('path');
const webpack = require('webpack');
const cssnano = require('cssnano');
const merge = require('webpack-merge');
const autoprefixer = require('autoprefixer');
const LessFuncPlugin = require('less-plugin-functions');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const BabelHelper = require('./helpers/babel-helper');
const IndexPageHelper = require('./helpers/index-helper');
const ExternalHelper = require('./helpers/external-helper');
const logger = require('../../logger');

const DepsPlugin = require('./plugins/deps-plugin');
const FlagsPlugin = require('./plugins/flags-plugin');


const baseContext = process.cwd();

exports = module.exports = (mode, projectConfig, kBceConfig, context) => {
    const indexPage = IndexPageHelper.resolve(projectConfig, kBceConfig, baseContext);
    const inject = kBceConfig.indexInject == null ? projectConfig.indexInject : kBceConfig.indexInject;
    const eslConf = JSON.stringify(projectConfig.eslConf || {});
    // 针对私有项目打包时依赖的公网资源地址通过指定的resourceBasePath进行替换，保障资源地址正确性
    const basePath = kBceConfig.resourceBasePath || (mode === 'production' ? '' : projectConfig.proxyTarget); // 区分下mode，buil时默认为空，减少参数设置
    const resourceBasePath = context.sdk ? ''
        : basePath.endsWith('/') ? basePath.substr(0, basePath.length - 1) : basePath;
    const moduleConf = eslConf.replace(/\${resourceBasePath}/g, resourceBasePath);
    // 使用优先级为：环境变量 > 指定templateId > template模板
    const templateId = process.env.templateId || context.templateId || projectConfig.templateId;
    const templateSource = process.env.templateId
    ? '环境变量' : (context.templateId ? '指定templateId' : (projectConfig.templateId ? 'template模板' : ''));
    templateId && logger.info(`当前使用的templateId来自${templateSource}；templateId信息：`, templateId);
    // 是否可以使用公网资源
    const public = !!(kBceConfig.public === undefined
        ? projectConfig.public === undefined
            ? true
            : projectConfig.public
        : kBceConfig.public);
    return merge.smart(projectConfig.webpack || {}, {
        mode: 'none',

        context: baseContext,

        entry: context.sdk ? {} : {
            bootstrap: require.resolve(process.cwd())
        },

        output: {
            library: context.sdk ? undefined : `${kBceConfig.pathname}/bootstrap`,
            filename: '[name].js',
            libraryTarget: context.sdk ? 'umd' : 'amd',
            path: path.join(baseContext, 'dist')
        },

        resolve: {
            extensions: ['.js', '.ts', '.json']
        },

        module: {
            rules: [
                {
                    test: /\.(ts|js)$/,
                    exclude: /node_modules/,
                    use: {
                        loader: 'babel-loader',
                        options: BabelHelper.getConfig(context, kBceConfig)
                    }
                },
                {
                    test: /\.(css|less)$/,
                    use: [
                        {loader: 'style-loader'},
                        {loader: 'css-loader'},
                        {
                            loader: 'postcss-loader',
                            options: {
                                plugins: [
                                    cssnano({preset: 'default'}),
                                    autoprefixer()
                                ]
                            }
                        },
                        {
                            loader: 'less-loader',
                            options: {
                                plugins: [new LessFuncPlugin()]
                            }
                        }
                    ]
                },
                // SVG Font
                {
                    test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
                    use: {
                        loader: 'url-loader',
                        options: {
                            limit: 10000,
                            mimetype: 'image/svg+xml'
                        }
                    }
                },
                // Common Image Formats
                {
                    test: /\.(?:ico|gif|png|jpg|jpeg|webp)$/,
                    use: 'url-loader'
                }
            ]
        },

        plugins: [
            new webpack.EnvironmentPlugin({
                VERSION: JSON.stringify(process.env.VERSION || 'DEV'),
                NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'production')
            }),

            ...(indexPage ? [
                new HtmlWebpackPlugin({
                    filename: 'index.html',
                    template: require.resolve(indexPage),
                    inject: inject == null ? 'body' : inject,
                    scope: kBceConfig.pathname,
                    resourceBasePath,
                    moduleConf,
                    modules: kBceConfig.flags,
                    templateName: kBceConfig.template,
                })
            ] : []),

            ...(templateId && !context.sdk && kBceConfig.flags && !projectConfig.isPrivateFeatureListFlag ? [
                new FlagsPlugin({
                    modules: kBceConfig.flags,
                    template: kBceConfig.template,
                    runtime: kBceConfig.runtime,
                    templateId,
                    path: path.join(baseContext, 'dist'),
                    pathname: kBceConfig.pathname,
                    public,
                })
            ] : []),

            ...(!context.sdk ? [
                new DepsPlugin({
                    projectConfig,
                    kBceConfig,
                    public
                })
            ] : [])
        ],

        externals: ExternalHelper.resolve(kBceConfig, projectConfig, context)
    });
};
