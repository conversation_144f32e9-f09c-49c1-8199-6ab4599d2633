/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.config.prod.js
 * <AUTHOR>
 */

const webpack = require('webpack');
const merge = require('webpack-merge');
const UglifyJSPlugin = require('uglifyjs-webpack-plugin');

const baseConfig = require('./webpack.config.base');

module.exports = class WebpackConfigBuilder {
    static async build(projectConfig = {}, kBceConfig = {}, context) {
        return merge.smart(baseConfig('production', projectConfig, kBceConfig, context), {
            mode: 'production',

            devtool: false,

            plugins: kBceConfig?.webpack?.optimization ? [
                new webpack.NoEmitOnErrorsPlugin()
            ] : [
                new webpack.NoEmitOnErrorsPlugin(),
                new UglifyJSPlugin({
                    parallel: true,
                    sourceMap: true
                })
            ]
        }, kBceConfig.webpack || {});
    }
};
