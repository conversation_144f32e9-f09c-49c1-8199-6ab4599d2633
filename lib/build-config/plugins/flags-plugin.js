/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file flag.plugin.js
 * <AUTHOR> (<EMAIL>)
 */

const fs = require('fs');
const path = require('path');
const { ConcatSource } = require('webpack-sources');
const ExternalModule = require('webpack/lib/ExternalModule');
const CommonJsRequireDependency = require('webpack/lib/dependencies/CommonJsRequireDependency');

const { http } = require('../../util');

module.exports = class {
    constructor(options) {
        this.templateId = options.templateId;
        this.template = options.template;
        this.public = options.public;
        this.runtime = options.runtime || '@baidu/bce-runtime';
        this.modules = Array.isArray(options.modules) ? options.modules : [options.modules];
        this.modules = this.modules.map(m => m.toLocaleLowerCase());
        this.exportName = `@builtin/${this.modules[0]}/flags`;
        this.path = options.path;
        this.pathname = options.pathname;
        if (!this.templateId || !this.modules.length) {
            throw new Error(`Error: FlagsPlugin init error, templateId = ${this.templateId} modules = ${this.modules}`);
        }
    }

    renderSource(isDevelopment) {
        const flagSourceURLs = this.modules.map(
            moduleName => {
                const filePath = `${moduleName.toLowerCase()}/type-definition@${this.templateId}`;
                return `https://console-center.bj.bcebos.com/type-definition/${filePath}`;
            }
        );
        return new Promise((resolve, reject) => {
            if (isDevelopment) {
                const f = flagSourceURLs.map((u, i) => `f${i + 1}`);
                const a = `(${f.join(').concat(')})`;

                return resolve(
                    `
                    (function(global){
                        global.__TEMPLATE = '${this.templateId}';
                        define(
                            '${this.exportName}',
                            ['${this.runtime}', '${flagSourceURLs.join("','")}'],
                            function(r,${f.join(',')}){
                                with(r.ServiceFactory){
                                    register('$flag', create('$flag', ${a}, '${this.template}'))
                                }
                        });
                    })(window);
                `.replace(/\s+/g, ' ')
                );
            }

            const reqs = flagSourceURLs.map(item => http.get(`${item}.json`));

            Promise.all(reqs).then(res => {
                const f = res.reduce((x, y) => {
                    const o = JSON.parse(y);
                    return o.code && o.code === 'NoSuchKey' ? x : x.concat(o); // 过滤加载失败的flag
                }, []);

                // 不使用公网资源时，运行时读取flag.js配置
                if (!this.public) {
                    if (!fs.existsSync(this.path)) {
                        fs.mkdirSync(this.path);
                    }
                    fs.writeFileSync(
                        path.join(this.path, 'flag.js'),
                        `define(function(){return ${JSON.stringify(f)};})`
                    );
                }

                return resolve(
                    `
                    (function(global){
                        global.__TEMPLATE = '${this.templateId}';
                        define(
                            '${this.exportName}',
                            ['${this.runtime}'${this.public ? '' : ", '/" + this.pathname + "/flag'"}],
                            function(r${this.public ? '' : ', f'}){
                                with(r.ServiceFactory){
                                    register('$flag', create('$flag', ${
                                        this.public ? JSON.stringify(f) : 'f'
                                    }, '${this.template}'))
                                }
                        });
                    })(window);
                `.replace(/\s+/g, ' ')
                );
            }, reject);
        });
    }

    apply(compiler) {
        compiler.hooks.normalModuleFactory.tap('FlagsPlugin', nmf => {
            nmf.hooks.factory.tap('FlagsPlugin', factory => (data, callback) => {
                return data.request === this.exportName
                    ? callback(null, new ExternalModule(this.exportName, 'amd', this.exportName))
                    : factory(data, callback);
            });
        });

        compiler.hooks.compilation.tap('FlagsPlugin', compilation => {
            compilation.hooks.succeedModule.tap('FlagsPlugin', m => {
                m.dependencies.unshift(new CommonJsRequireDependency(this.exportName, null));
            });

            compilation.hooks.optimizeChunkAssets.tapAsync('FlagsPlugin', async (chunks, callback) => {
                const sources = [];
                for (const chunk of chunks) {
                    // eslint-disable-line no-restricted-syntax
                    if (!chunk.canBeInitial()) {
                        continue; // eslint-disable-line no-continue
                    }

                    for (const file of chunk.files) {
                        // eslint-disable-line no-restricted-syntax
                        if (/(bootstrap|index)\.js$/.test(file)) {
                            sources.push(file);
                        }
                    }
                }
                this.renderSource(compiler.options.mode === 'development').then(res => {
                    sources.forEach(file => compilation.updateAsset(file, old => new ConcatSource(res, '\n', old)));
                    callback();
                }, callback);
            });
        });
    }
};
