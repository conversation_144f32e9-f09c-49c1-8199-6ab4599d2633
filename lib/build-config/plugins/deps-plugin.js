/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file 依赖库处理
 * <AUTHOR> (<EMAIL>)
 */
const fs = require('fs');
const url = require('url');
const path = require('path');
const acorn = require('acorn');
const acornLoose = require('acorn-loose');
const { execSync } = require('child_process');
const { ConcatSource } = require('webpack-sources');
const extractAMDDeps = require('dependency-cruiser/src/extract/ast-extractors/extract-amd-deps');

const { http } = require('../../util');
// const logger = require('../../../logger');
const PackageProvider = require('../../providers/pkg-provider');

const PLUGIN_NAME = 'DepsPlugin';

// npm link查找模块安装包使用
module.paths.unshift(path.join(process.cwd(), 'node_modules'));

module.exports = class {
    constructor(options) {
        // 全部依赖库的地址收集
        this.deps = [];
        // 该打包模块使用到的依赖库
        this.usedDeps = new Set();

        this.maps = (options.projectConfig.moduleConf.map && options.projectConfig.moduleConf.map['*']) || {};

        // 保存业务侧模块新增 esl 映射规则
        this.customMaps = options.kBceConfig.eslConfigAddMap;

        // 是否可以使用公网资源
        this.public = options.public;

        if (options.projectConfig.moduleConf) {
            Object.keys(options.projectConfig.moduleConf.paths).forEach(lib => {
                this.deps.push({
                    libName: lib,
                    libPath: options.projectConfig.moduleConf.paths[lib] + '.js',
                    eslConfig: true // 首页已经有了，在esl配置中
                });
            });
        }

        if (options.kBceConfig.dependencies) {
            Object.entries(options.kBceConfig.dependencies).forEach(item => {
                const { libName, libPath, libVersion } = PackageProvider.normalizeDeps(item);

                this.deps.push({
                    libName: `${libName}@${libVersion}`,
                    libPath: libPath,
                    // 产品模块自己安装的工具包，在module-conf中未进行配置
                    eslConfig: false
                });
            });
        }
        // 重要 不要删除
        this.deps = this.deps.reverse();
    }

    get exoticRequireStrings() {
        return ['window.require'];
    }

    // 得到符合esl配置规则的模块资源地址
    resolveRequirePath(dep) {
        let reqPath = dep.libPath;

        if (!this.public) {
            reqPath = reqPath.replace('https://code.bdstatic.com', '.').replace('https://bce.bdstatic.com', '.');
        }

        return reqPath.replace(/\.js$/, '');
    }

    resolveDepFiles(reqDep) {
        reqDep = this.maps[reqDep] || reqDep;
        const dep = this.deps.find(item => reqDep === item.libName || reqDep.startsWith(`${item.libName}/`));

        // 没有配置信息
        if (!dep) {
            return;
        }

        // 配置的是非公网地址，公网的话顺带也帮忙做一些资源的下载工作
        // 大项目不这么做，默认module中是已经在环境中存在的私有化依赖
        // 自定义的小项目，为了更方便的做整个项目的私有化输出，module-conf中可进行公有云资源配置
        if (dep.eslConfig && !/bdstatic/.test(dep.libPath)) {
            return;
        }

        if (reqDep === dep.libName) {
            // @baidu/bce-xx-sdk  @npm/loadash
            return [dep];
        } else {
            // @baidu/bce-xx-sdk@1.2.3/san => 'san'
            return [dep, reqDep.replace(`${dep.libName}/`, '')];
        }
    }

    async renderConfig(compilation, logger) {
        const files = Array.from(this.usedDeps)
            .map(reqDep => this.resolveDepFiles(reqDep))
            .filter(item => !!item);

        if (files.length && !this.public) {
            await this.depsLocalize(files, compilation, logger);
        }

        const pathConfig = this.deps.reduce((config, dep) => {
            !dep.eslConfig && (config[dep.libName] = this.resolveRequirePath(dep));
            return config;
        }, {});
        // 业务侧存在 dependencies || 存在dependenciesConfigMap
        return (Object.keys(pathConfig).length || this.customMaps)
            ? `
                (function () {
                    require.config({
                        ${Object.keys(pathConfig).length ? 'paths:' + JSON.stringify(pathConfig) : ''}
                        ${this.customMaps ? ',map:' + JSON.stringify({'*': {...this.maps, ...this.customMaps}}) : ''}
                    });
                })();
            `.replace(/\s+/g, ' ')
            : '';
    }

    // AMD资源的相对资源路径获取
    resolveAMDRelapath(resourcePath, relaMod) {
        resourcePath = resourcePath.replace(/\.js$/, '/');
        return url.resolve(resourcePath, relaMod);
    }

    async downloadResource(dep, relaMod) {
        const { libName, libPath } = dep;

        let resourcePath = libPath;
        relaMod && (resourcePath = this.resolveAMDRelapath(resourcePath, relaMod) + '.js');

        try {
            return http.get(resourcePath);
        } catch (e) {}

        // npm包
        if (resourcePath.match(/^http(s)?:\/\/code\.bdstatic\.com\/npm/)) {
            const libVersion = resourcePath
                .replace(/^http(s)?:\/\/code\.bdstatic\.com\/npm\//, '')
                .replace(`${libName}@`, '')
                .split('/')[0];
            const installName = `@deps/${libName}`;

            execSync(`npm install ${installName}@npm:${libName}@${libVersion}`);
            return fs.readFileSync(require.resolve(installName + resourcePath.split(`${libName}@${libVersion}`)[1]));
        }

        // bce 官方提供的sdk包
        if (resourcePath.match(/^http(s)?:\/\/bce\.bdstatic\.com\//)) {
            return http.get(
                resourcePath.replace(/^http(s?):\/\/bce\.bdstatic\.com/, 'https://bj.bcebos.com/v1/bce-cdn')
            );
        }

        throw `Unknown source of ${resourcePath}, Please use npm or bce sdk, <NAME_EMAIL>！`;
    }

    async depsLocalize(files, compilation, logger) {
        const cache = new Set();

        while (files.length) {
            const file = files.shift();
            const [dep, relaMod] = file;
            const { libName, libPath } = dep;
            const cacheKey = `${libName} ${relaMod || ''}`;

            if (cache.has(cacheKey)) {
                continue;
            }

            let jsSource = null;
            try {
                jsSource = await this.downloadResource(dep, relaMod);
            } catch (e) {
                logger.error(`Download file '${libPath}' failed! Message: ${e}`);
                throw e;
            }

            cache.add(cacheKey);

            let deployPath = this.resolveRequirePath(dep) + '.js';
            relaMod && (deployPath = this.resolveAMDRelapath(deployPath, relaMod) + '.js');

            compilation.emitAsset(deployPath, {
                source: () => jsSource,
                size: () => jsSource.length
            });

            // 文件内容依赖分析
            const deps = this.getDepsFromSource(jsSource);
            if (deps.length) {
                deps.forEach(({ module }) => {
                    // ../path or ./path 相对模块
                    if (module.match(/^(\.)[1, 2]\//)) {
                        files.push([dep, module]);
                        return;
                    }

                    const depFiles = this.resolveDepFiles(module);
                    // 依赖库第三方依赖库
                    depFiles && files.push(depFiles);
                });
            }
        }
    }

    getDepsFromSource(jsSource) {
        const deps = [];
        let ast;

        try {
            ast = acorn.parse(jsSource, {
                sourceType: 'module',
                ecmaVersion: 11
            });
        } catch (pError) {
            return acornLoose.parse(jsSource, {
                sourceType: 'module'
            });
        }

        extractAMDDeps(ast, deps, this.exoticRequireStrings);

        return deps;
    }

    apply(compiler) {
        let entries = [];

        compiler.hooks.entryOption.tap(PLUGIN_NAME, (context, entry) => {
            if (typeof entry === 'object' && !Array.isArray(entry)) {
                entries = Object.keys(entry);
                return;
            }
            const logger = compiler.getInfrastructureLogger(PLUGIN_NAME);
            logger.error('Entry must be an object');
            process.exit(1);
        });

        compiler.hooks.compilation.tap(PLUGIN_NAME, compilation => {
            compilation.hooks.finishModules.tap(PLUGIN_NAME, ms => {
                ms.forEach(m => {
                    // eslint-disable-next-line
                    m.dependencies.forEach(d => {
                        d.module && d.module.external && this.usedDeps.add(d.module.request);
                    });
                });
            });

            compilation.hooks.optimizeChunkAssets.tapAsync(PLUGIN_NAME, async (chunks, callback) => {
                const sources = [];

                for (const chunk of chunks) {
                    // eslint-disable-line no-restricted-syntax
                    if (!chunk.canBeInitial()) {
                        continue; // eslint-disable-line no-continue
                    }

                    for (const file of chunk.files) {
                        // eslint-disable-line no-restricted-syntax
                        const isMainFile = entries.filter(en => file.indexOf(en) > -1).length;
                        if (isMainFile) {
                            sources.push(file);
                        }
                    }
                }

                if (!sources.length) {
                    return callback();
                }

                const logger = compilation.getLogger(PLUGIN_NAME);

                try {
                    const res = await this.renderConfig(compilation, logger);
                    sources.forEach(
                        // eslint-disable-next-line
                        file => compilation.updateAsset(file, old => new ConcatSource(res, '\n', old))
                    );
                    callback();
                } catch (err) {
                    callback(err);
                }
            });
        });
    }
};
