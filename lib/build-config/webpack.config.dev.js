/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.config.dev.js
 * <AUTHOR>
 */

const url = require('url');
const webpack = require('webpack');
const merge = require('webpack-merge');

const baseConfig = require('./webpack.config.base');
const DebugHelper = require('./helpers/debug-helper');
const MockHelper = require('./helpers/mock-helper');
const {findFreePort} = require('../util');

module.exports = class WebpackConfigBuilder {
    static async build(projectConfig = {}, kBceConfig = {}, context) {

        const kPort = await findFreePort(8989);
        const proxyTarget = kBceConfig.proxyTarget || projectConfig.proxyTarget;
        let proxy = {};
        let publicPath = null;

        if (proxyTarget) {
            const projectProxy = projectConfig.webpack.devServer.proxy;
            // mock config
            const mockProxy = context.mock ? MockHelper.resolve(kBceConfig) : {};
            // merge proxy
            Object.keys(projectProxy).forEach(key => {
                proxy[key] = merge.smart(
                    projectProxy[key],
                    mockProxy,
                    {
                        target: proxyTarget,
                        onProxyReq(proxyReq) {
                            // 某些接口会验证origin
                            proxyReq.setHeader('origin', proxyTarget);
                        }
                    }
                );
            });

            const proxyUrl = url.parse(proxyTarget);
            proxyUrl.port = kPort;
            proxyUrl.hostname = `localhost.${proxyUrl.hostname}`;
            delete proxyUrl.host;
            publicPath = url.format(proxyUrl);
        }
        else {
            publicPath = `http://localhost:${kPort}`;
        }

        if (!context.sdk) {
            // 加模块path
            publicPath += `${publicPath.endsWith('/') ? '' : '/'}${kBceConfig.pathname}/`;
        }

        return merge.smart(baseConfig('development', projectConfig, kBceConfig, context), {
            mode: 'development',

            plugins: [
                new webpack.LoaderOptionsPlugin({
                    debug: true
                }),


                ...(kBceConfig.debug ? [
                    new webpack.BannerPlugin({
                        entryOnly: true,
                        // test: 'bootstrap.js',
                        include: /\.js$/,
                        banner: DebugHelper.resolve(kBceConfig),
                        raw: true
                    })
                ] : [])
            ],

            devServer: merge.smart({
                publicPath,
                port: kPort,
                host: '0.0.0.0',
                compress: true,
                noInfo: true,
                stats: 'errors-only',
                inline: true,
                lazy: false,
                hot: true,
                disableHostCheck: true,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Credentials': true
                },
                watchOptions: {
                    aggregateTimeout: 300,
                    ignored: /node_modules/,
                    poll: 100
                },
                historyApiFallback: {
                    verbose: true,
                    disableDotRule: false
                }
            }, {proxy})
        }, kBceConfig.webpack || {});
    }
};
