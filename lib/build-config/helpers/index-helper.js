/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file index page获取处理
 * <AUTHOR>
 */
const path = require('path');
const fs = require('fs');

// npm link查找模块安装包使用
module.paths.unshift(path.join(process.cwd(), 'node_modules'));
module.exports = class IndexPageHelper {
    static resolve(projectConfig, kBceConfig, contextBase) {

        if (kBceConfig && kBceConfig.indexPage) {
            return kBceConfig.indexPage;
        }

        let index = path.resolve(contextBase, 'index.html');
        if (fs.existsSync(index)) {
            return index;
        }

        if (kBceConfig.template && projectConfig.root) {
            return require.resolve(`${projectConfig.root}/index.html`);
        }

        return null;
    }
};
