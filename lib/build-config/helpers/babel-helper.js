/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file babel配置.js
 * <AUTHOR>
 */
const path = require('path');
const kPresetEnv = require('@babel/preset-env').default;
const kTSPreset = require('@babel/preset-typescript').default;
const kDynamicImportPlugin = require('@babel/plugin-syntax-dynamic-import').default;
const kProposalDecoratorPlugin = require('@babel/plugin-proposal-decorators').default;
const kClassPropertiesPlugin = require('@babel/plugin-proposal-class-properties').default;
const kTSTransform = require('@babel/plugin-transform-typescript').default;

module.exports = class {
    static getConfig(context, kBceConfig) {

        const {babelOptions: {presets = [], plugins = []} = {}} = kBceConfig;

        // npm link查找模块安装包使用
        module.paths.unshift(path.join(process.cwd(), 'node_modules'));

        return {
            cacheDirectory: process.env.NODE_ENV !== 'production',
            presets: [
                [
                    kPresetEnv,
                    {targets: '> 0.5%, last 2 versions, Firefox ESR, not dead'} // https://browserl.ist/
                ],
                kTSPreset,
                ...presets
            ],
            plugins: [
                kTSTransform,
                kDynamicImportPlugin,
                [kProposalDecoratorPlugin, {legacy: true}],
                [kClassPropertiesPlugin, {loose: true}],
                ...plugins
            ].concat(
                context.i18n
                    ? [[
                        // eslint-disable-next-line
                        require('@baiducloud/i18n/transform-plugin'),
                        {library: '@baiducloud/i18n', output: 'dist/i18n'}
                    ]] : []
            )
        };
    }
};
