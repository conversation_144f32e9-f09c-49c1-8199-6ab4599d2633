/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file 依赖库联调
 * <AUTHOR>
 */
const path = require('path');
const fs = require('fs');
const PackageProvider = require('../../providers/pkg-provider');

module.exports = class DebugHelper {
    static resolve(kBceConfig) {
        const dependencies = kBceConfig.dependencies || {};

        return Object.entries(kBceConfig.debug).map(([libName, libPath]) => {

            let libVersion = null;
            if (libName in dependencies) {
                libVersion = PackageProvider.normalizeDeps([libName, dependencies[libName]]).libVersion;
            }
            // 由于处理模块自定义版本和bce-verisons版本重复时，两者定义了别名，因此版本相同可能会导致其查找源模块地址
            return `
                require.addLoader((context, req, load, config) => {
                    const libName = '${libVersion || ''}'
                        && require.toUrl('${libName}') !== require.toUrl('${libName}@${libVersion}')
                            ? '${libName}@${libVersion}'
                            : '${libName}';
                    if (context.id.startsWith(libName)) {
                        const ext = context.id.replace(libName, '');
                        return '${libPath}'.replace(/\\.js$/, ext + '.js');
                    }
                });
            `.replace(/\s+/g, ' ');
        }).join(' ');
    }
};
