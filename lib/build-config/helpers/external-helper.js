/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file 默认external依赖库
 * <AUTHOR>
 */

const PkgProvider = require('../../providers/pkg-provider');

module.exports = class ExternalHelper {

    static resolve(kBceConfig, projectConfig, context) {
        const {dependencies} = kBceConfig;
        let externals = [];

        // 产品模块
        if (!context.sdk) {
            const {moduleConf = {}} = projectConfig;

            externals = [
                'framework',
                ...Object.keys(moduleConf.paths || {}),
                ...Object.values(moduleConf.bundles || {}).reduce((ctx, val) => ctx.concat(val), []),
                ...Object.keys(moduleConf.map && moduleConf.map['*'] || {}).reduce((ctx, val) => ctx.concat(val), [])
            ];
        }

        if (dependencies) {
            const deps = Object.entries(dependencies).map(d => PkgProvider.normalizeDeps(d));
            externals.unshift((context, request, callback) => {
                const installedDep = deps
                    .find(d => request === d.libName || request.startsWith(d.libName + '/'));
                if (!installedDep) {
                    return callback();
                }

                // eslint-disable-next-line max-len
                const {libName, libVersion} = installedDep;
                return callback(null, `umd ${libName}@${libVersion}${request.replace(libName, '')}`);
            });
        }

        return externals || [];
    }
};
