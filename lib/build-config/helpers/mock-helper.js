/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file mock模式配置
 * <AUTHOR>
 */

const path = require('path');
const fs = require('fs');
const zlib = require('zlib');
const fx = require('mkdir-recursive');
const {pathToRegexp} = require('path-to-regexp');

const logger = require('../../../logger');

const DEFAULT_MOCKRC = {
    root: '.mockup', // mock数据目录
    expired: 30, // 有效期（天）----暂时无用
    caching: true, // Boolean 是否自动缓存
    rules: [] // 说明哪些接口需求本地mock
};

module.exports = class MockHelper {
    static resolve(kBceConfig) {
        const proxyConfig = {
            // logLevel: 'debug' | 'info'
        };
        const mockrc = Object.assign(DEFAULT_MOCKRC, kBceConfig.mockup || {});
        const mockRoot = path.resolve(process.cwd(), mockrc.root, kBceConfig.template);

        proxyConfig.bypass = (req, res, proxyOptions) => {
            if (req.headers.accept.indexOf('html') === -1) {
                const {pathname} = req._parsedUrl;
                const cachePath = path.resolve(mockRoot, `./${pathname}`, `${req.method}.js`.toLowerCase());
                const hasRule = mockrc.rules && mockrc.rules.length;
                let needMock = !hasRule;

                if (hasRule) {
                    // eslint-disable-next-line no-restricted-syntax
                    for (const item of mockrc.rules) {
                        const url = typeof item === 'string' ? item : item.path;
                        if (pathToRegexp(url).exec(pathname)) {
                            needMock = true;
                            break;
                        }
                    }
                }

                if (needMock) {
                    if (fs.existsSync(cachePath)) {
                        let response = null;
                        try {
                            res.set({'X-Mockup-By': 'bce-cli'});
                            delete require.cache[cachePath];

                            // eslint-disable-next-line import/no-dynamic-require
                            const mockModule = require(cachePath);
                            if (typeof mockModule === 'function') {
                                response = mockModule(req);
                            }
                            else {
                                // eslint-disable-next-line max-len
                                logger.error(`Mock data error of api ==> ${req.method} ${pathname}, detail: export must be function`);
                                return false;
                            }

                            logger.info(`Mockup ${pathname} from cache data`);

                            return res.json(response);
                        }
                        catch (err) {
                            if (!err.name.indexOf('[ERR_HTTP_HEADERS_SENT]')) { // TODO:lvxiaojiao
                                // eslint-disable-next-line max-len
                                logger.error(`Mock data error of api ==> ${req.method} ${pathname}, detail: ${err.toString()}`);
                            }
                            // return false;
                        }
                    }
                    else {
                        logger.warn(`Not found mock data of api ==> ${req.method} ${pathname}`);
                    }
                }
            }
        };

        if (mockrc.caching) {
            proxyConfig.selfHandleResponse = true;
            proxyConfig.onProxyRes = (proxyRes, req, res) => {
                const pathname = req._parsedUrl.pathname;
                const cachePath = path.resolve(mockRoot, `./${pathname}`, `${req.method}.js`.toLowerCase());
                let buffers = [];

                // collect chunk
                proxyRes.on('data', data => {
                    if (proxyRes.headers['transfer-encoding'] === 'chunked') {
                        // console.log(`Receive ${req.method} ${pathname} chunk ${data.length} 字节`);
                        buffers.push(data);
                    }
                    else {
                        buffers = data;
                    }
                });

                proxyRes.on('end', async () => {
                    try {
                        if (!buffers.length) {
                            return res.end('Empty data');
                        }

                        // parse response
                        if (proxyRes.headers['content-encoding'] === 'gzip') { // 默认gzip
                            if (proxyRes.headers['transfer-encoding'] === 'chunked') {
                                buffers = Buffer.concat(buffers);
                            }
                            buffers = zlib.gunzipSync(buffers).toString('utf8');
                        }

                        const response = JSON.stringify(JSON.parse(buffers), null, 4);

                        res.end(response);

                        // 不对重定向数据进行缓存
                        const resJson = JSON.parse(response);
                        const redirect = !resJson.success && resJson.message && resJson.message.redirect;

                        // cache response
                        if (!redirect && !fs.existsSync(cachePath)) {
                            const dir = path.resolve(cachePath, '..');

                            try {
                                fx.mkdirSync(dir);
                            }
                            catch (ex) {}

                            try {
                                const resScript = `module.exports = () => {\r\nreturn ${response}\r\n}`;
                                fs.writeFileSync(cachePath, resScript, {flags: 'a+'});
                            }
                            catch (ex) {}

                            logger.info(`Complete ${pathname} data caching`);
                        }
                    }
                    catch (err) {
                        logger.error(`Failed to parse and cache ${pathname} data:${err}`);
                    }
                });
            };
        }


        return proxyConfig;
    }
};
