/**
 * @file 工具模块启动配置
 */

module.exports = {
    // 模块相关配置
    service: '{service}', // 在P3M平台注册的service type，默认name全大写
    pathname: '{pathname}', // 表明访问的path，默认代码库后几位字母
    flags: '{flags}', // 获取的功能清单的模块，默认是service

    // mock配置
    mockup: {
        caching: true,
        rules: ['/api/{pathname}/(.*)'], // 默认值 /api/pathname/*
        root: '.mockup' // 默认 /.mockup
    },

    // debug调试
    debug: {
        // '@baiducloud/runtime': 'http://localhost:8989/runtime.js',
        // '@baiducloud/xxx-sdk': 'http://localhost:8990/xxx-sdk.js'
    },

    // 代理地址
    // proxyTarget: 'https://qasandbox.bcetest.baidu.com',

    // 需要排除的依赖，模块会帮助配置esl，sdk则需要依赖模块的配置
    dependencies: {
        // '@baiducloud/xxx-sdk': '@baiducloud/fe-xxx-sdk/x.x.x.x/xxx-sdk.js'
    },

    // 自定义webpack，可选，默认一个入口 index.js/index.ts
    webpack: {
        entry: {},
        externals: [
            '@baiducloud/runtime'
        ]
    }
};
