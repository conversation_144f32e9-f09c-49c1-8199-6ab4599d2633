/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * @file proxy node
 * <AUTHOR> (<EMAIL>)
 */

const fs = require('fs');
const url = require('url');
const path = require('path');
const http = require('http');
const https = require('https');

const Deamon = require('../deamon');
const ProxyProvider = require('../proxy-server');
const logger = require('../../logger');

// npm link查找模块安装包使用
module.paths.unshift(path.join(process.cwd(), 'node_modules'));
module.exports = class ProxyNode {
    static async start(options) {
        const { module, target, template, pathname } = options;
        try {
            // 开启守护进程
            await Deamon.start(template);
            logger.info(`Proxy of project ${template} has been inited!`);
        } catch (e) {
            logger.error(`Proxy of project ${template} init failed!`);
            throw e;
        }

        const proxyPath = url.parse(target);

        try {
            proxyPath.port = await ProxyNode._send(
                {
                    action: ProxyProvider.NotifyEvents.START,
                    module,
                    target,
                    pathname: `/${pathname}/`,
                    pid: process.pid
                },
                template
            );
            logger.info('Proxy register Succeed!');
        } catch (e) {
            logger.error(`Proxy register failed! ${e.toString()}`);
            throw e;
        }

        // 当前产品关闭编译服务后，需要通知代理服务器把服务下掉，避免还一直代理过来导致用户不知道是服务挂了
        process.on('SIGINT', async () => {
            await ProxyNode.stop(options);
            process.exit(0);
        });

        delete proxyPath.host;
        return url.format(proxyPath);
    }

    static async stop({ module, target, template, pathname }) {
        try {
            await ProxyNode._send(
                {
                    action: ProxyProvider.NotifyEvents.STOP,
                    module,
                    target,
                    pathname: `/${pathname}/`,
                    pid: process.pid
                },
                template
            );
            logger.info('Proxy removed!');
        } catch (e) {
            logger.error(`Proxy remove failed! ${e.toString()}`);
        }
    }

    static _send(message, template) {
        let isHttps = message.target.startsWith('https');
        let data = JSON.stringify(message);

        return new Promise(async (rs, rj) => {
            const port = await Deamon.port(template);
            const req = (isHttps ? https : http).request(
                {
                    method: 'POST',
                    port,
                    protocol: isHttps ? 'https:' : 'http:',
                    path: ProxyProvider.NotifyURL,
                    rejectUnauthorized: false,
                    headers: {
                        'Content-Type': 'application/json',
                        'Content-Length': data.length
                    }
                },
                async res => {
                    res.setEncoding('utf8');

                    const body = JSON.parse(await ProxyNode._parseBody(res));

                    if (body.success) {
                        return rs(port);
                    }
                    rj(new Error(body.message));
                }
            );

            req.write(data);
            req.end();
        });
    }

    static _parseBody(res) {
        return new Promise((rs, rj) => {
            let body = '';
            res.on('data', chunk => {
                body += chunk;
            })
                .on('end', () => {
                    rs(body);
                })
                .on('error', () => {
                    rj();
                });
        });
    }
};
