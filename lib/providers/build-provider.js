/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file webpack build
 * <AUTHOR>
 */

const fs = require('fs');
const path = require('path');
const webpack = require('webpack');
const {blueBright} = require('chalk');
const {execSync} = require('child_process');
const WebpackDevServer = require('webpack-dev-server');

const logger = require('../../logger');
const {resolveDefaultConfig, resolveProjectConfig} = require('../util');
const WebpackConfigBuilder = require('../build-config/webpack.config.prod');


module.exports = class BuildProvider {
    static async action(context) {
        // eslint-disable-next-line
        const kBceConfig = resolveDefaultConfig(require(path.resolve(process.cwd(), context.config)), context);
        const projectConfig = resolveProjectConfig('production', kBceConfig, context);
        const webpackConfig = await WebpackConfigBuilder.build(projectConfig, kBceConfig, context);

        delete webpackConfig.devServer;

        webpack(webpackConfig, (err, stats) => {
            if (stats) {
                process.stdout.write(`${stats.toString({colors: true})}\n`);
            }

            if (err) {
                console.log(err);
            }

            if (err || stats.hasErrors()) {
                process.exit(1);
            }
            else {
                logger.info('Build Finished!');

                // 是否进行国际化，上传语料
                if (context.i18n && kBceConfig.service) {
                    const service = kBceConfig.service;
                    const root = path.resolve(process.cwd(), 'dist/i18n');

                    if (!fs.existsSync(root)) {
                        logger.error(`I18n terms directory %o is not found, ${root}`);
                        process.exit(-1);
                    }

                    logger.info('Start to upload i18n terms');
                    execSync(
                        `npx bce-i18n upload -d dist/i18n --module ${service} && rm -rf dist/i18n`,
                        {
                            env: {
                                ...process.env,
                                NODE_ENV: 'production'
                            },
                            cwd: process.cwd(),
                            stdio: [0, 1, 2]
                        }
                    );
                }
            }
        });
    }
};
