/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file pkg-provider.js
 * <AUTHOR> (<EMAIL>)
 */
const path = require('path');
const chalk = require('chalk');
const https = require('https');
const childProcess = require('child_process');
const logger = require('../../logger');

module.exports = class PkgProvider {

    static get MasterLibReg() {
        return /^((@baiducloud\/)|(@baidu\/bce-)|(@baidu\/sui))/;
    }

    static normalizeDeps([keyName, pathName]) {
        let libPath = null;
        let libVersion = null;

        if (keyName.startsWith('@npm/')) {
            // "@npm/humanize": "humanize@0.0.9/humanize.js"
            libVersion = pathName.replace(`${keyName.replace('@npm', '')}@`, '');
            libVersion = libVersion.substring(0, libVersion.indexOf('/'));
            libPath = `https://code.bdstatic.com/npm/${pathName}`;
        }
        else {
            // "@baiducloud/tag-sdk": "@baiducloud/fe-tag-sdk/1.0.15.2/tag-sdk.js"
            libVersion = pathName.match(/([\d]+\.){3}[\d]+/g);
            libPath = `https://bce.bdstatic.com/lib/${pathName}`;
        }
        return {libName: keyName, libPath, libVersion};
    }


    static list(context) {
        // eslint-disable-next-line
        const {dependencies = {}} = require(path.resolve(process.cwd(), context.config));

        console.log(chalk.gray(`\n\t${'-'.repeat(88)}\n`));
        Object.entries(dependencies).forEach(item => {
            const {libName, libVersion} = PkgProvider.normalizeDeps(item);

            console.log(chalk.greenBright(`\t ${libName}@${libVersion}`) + chalk.gray(` ==> ${item[1]}`));
        });
        console.log(chalk.redBright('\n\t ⚠️  Npm资源路径取决于`package.main`配置，自行检查资源是`minify`版本！'));
        console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
    }

    static search(installName) {
        let task = null;
        if (PkgProvider.MasterLibReg.test(installName)) {
            task = new Promise((resolve, reject) => {
                https.get('https://bce.bdstatic.com/console/static/online-config.js', res => {
                    if (res.statusCode !== 200) {
                        return reject(installName);
                    }

                    let buffer = '';
                    res.on('data', chunk => {
                        buffer += chunk;
                    });
                    res.on('end', () => {
                        try {
                            const onlineConf = JSON.parse(buffer.slice(24, -4));
                            const modulePath = onlineConf[installName].path
                                .replace('https://bce.bdstatic.com/lib/', '');

                            resolve([installName, `${modulePath}.js`]);
                        }
                        catch (e) {
                            reject(installName);
                        }
                    });
                }).on('error', () => reject(installName));
            });
        }
        else {
            task = new Promise(
                (resolve, reject) => childProcess.exec(
                    // eslint-disable-next-line
                    `npm view ${installName} main name version browser -json --registry=http://registry.npm.baidu-int.com`,
                    (err, data) => {
                        const {main, name, version, browser} = JSON.parse(data);

                        return err ? reject(name || installName)
                            : resolve([`@npm/${name}`, `${name}@${version}/${browser || main}`]);
                    }
                )
            );
        }

        return task.then(
            res => {
                console.log(chalk.gray('\n\t查找到以下安装包，添加到配置文件后重启即可完成引入: \n'));
                console.log(chalk.green(`\t'${res[0]}': '${res[1]}'\n\n`));
            },
            name => {
                console.log(chalk.gray(`\n\t${'-'.repeat(88)}\n`));
                console.log(chalk.red(`\t install ${name} failed!`));
                console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
            }
        );
    }
};
