/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * @file dev
 * <AUTHOR>
 */

const open = require('opn');
const path = require('path');
const webpack = require('webpack');
const {blueBright} = require('chalk');
const WebpackDevServer = require('webpack-dev-server');

const logger = require('../../logger');
const {resolveDefaultConfig, resolveProjectConfig} = require('../util');
const WebpackBuilderNode = require('../proxy-node');
const WebpackConfigBuilder = require('../build-config/webpack.config.dev');


module.exports = class DevProvider {
    static async action(context) {
        // eslint-disable-next-line
        const kBceConfig = resolveDefaultConfig(require(path.resolve(process.cwd(), context.config)), context);
        const projectConfig = resolveProjectConfig('development', kBceConfig, context);

        const webpackConfig = await WebpackConfigBuilder.build(projectConfig, kBceConfig, context);
        const devConf = webpackConfig.devServer;
        WebpackDevServer.addDevServerEntrypoints(webpackConfig, devConf);

        const compiler = webpack(webpackConfig);
        const server = new WebpackDevServer(compiler, {
            ...devConf,
            stats: {
                colors: true
            }
        });

        server.listen(devConf.port, devConf.host, async err => {
            if (err) {
                logger.error(err);
                process.exit(1);
            }

            // 单项目多模块场景启动代理，模拟前端微服务，方便模块之间跳转，要求使用项目定义的proxy target
            if (
                !context.sdk
                && context.template
                && !projectConfig.root.startsWith(process.cwd())
                && (!kBceConfig.proxyTarget || kBceConfig.proxyTarget === projectConfig.proxyTarget)
            ) {
                let proxyPath = devConf.publicPath;
                try {
                    proxyPath = await WebpackBuilderNode.start({
                        module: kBceConfig.service,
                        pathname: kBceConfig.pathname,
                        template: context.template,
                        target: proxyPath
                    });
                }
                catch (e) {
                    server.close();
                    process.exitCode = 1;
                    throw e;
                }

                logger.info(`Module added to project proxy, available on: ${blueBright(proxyPath)}`);

                await open(proxyPath);
                return;
            }
            // SDK
            if (context.sdk) {
                logger.info(`Lib availabel on: ${blueBright(devConf.publicPath)}`);
            }
            else {
                logger.info(`Module available on: ${blueBright(devConf.publicPath)}`);
                await open(devConf.publicPath);
            }
        });
    }
};
