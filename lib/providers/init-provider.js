/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file init-provider.js
 * <AUTHOR> (lv<PERSON><PERSON><PERSON><PERSON>@baidu.com)
 */
/** @type {*} */

const fs = require('fs');
const path = require('path');
const inquirer = require('inquirer');
const shell = require('shelljs');

const logger = require('../../logger');
const {http} = require('../util');
module.exports = class InitProvider {
    static async action(context) {

        const answers = await inquirer.prompt([
            {
                type: 'list',
                message: '请选择初始化类型',
                name: 'type',
                choices: [
                    {
                        name: '工具模块', value: 'sdk'
                    },
                    {
                        name: '产品模块', value: 'module'
                    }
                ]
            },
            {
                type: 'input',
                name: 'name',
                message: '名称（须符合"@baidu/bce-{name}"或者"@baidu/{name}"命名规范）：',
                validate(value) {
                    const name = value.trim();
                    if (/^@baidu\/bce-/.test(name) || /^@baidu\//.test(name)) {
                        return true;
                    }

                    return '请输入符合规范的名称';
                },
                when(aws) {
                    return aws.type === 'sdk';
                }
            },
            {
                type: 'input',
                name: 'service',
                message: '服务简称（在P3M平台注册的serviceType，默认名称全大写，例如：BCC）：',
                validate(value) {
                    const service = value.trim();
                    if (service.length) {
                        return true;
                    }

                    return '请输入服务简称';
                },
                when(aws) {
                    return aws.type === 'module';
                }
            },
            {
                type: 'input',
                name: 'pathname',
                message: '访问路径(默认与服务简称保持一致，例如：bcc）)：',
                validate(value) {
                    const pathname = value.trim();
                    if (pathname.length) {
                        return true;
                    }

                    return '请输入访问路径';
                },
                when(aws) {
                    return aws.type === 'module';
                }
            },
            {
                type: 'confirm',
                name: 'openFlag',
                message: '是否开启功能清单？',
                when(aws) {
                    return aws.type === 'module';
                }
            },
            {
                type: 'input',
                name: 'flags',
                message: '获取哪些服务的清单（英文逗号分隔，默认与服务简称保持一致，例如：BCC,CDS）：',
                validate(value) {
                    const flags = value.trim();
                    if (flags.length) {
                        return true;
                    }

                    return '请输入访问路径';
                },
                when(aws) {
                    return aws.type === 'module' && aws.openFlag;
                }
            },
            {
                type: 'confirm',
                name: 'init',
                message: '使用既定的模板帮您初始化模块？'
            }
        ]);

        if (answers.init) {
            const icodes = {
                module: {
                    name: 'bce-console/console-demo',
                    token: '091b196d-3435-4f17-be63-e08e1fd0fd65'
                },
                sdk: {
                    name: 'baiducloud/sdk-demo',
                    token: '81d9e844-a18a-4bff-845c-787865aba4f8'
                }
            };
            const module = `baidu/${icodes[answers.type].name}`;
            const vd = await http.get(`http://agile.baidu.com/api/agile/getReleaseInfoOutputUrl?module=${module}`);
            const version = JSON.parse(vd, null, 4).releaseVersion;

            // download
            shell.exec(`
                curl -L -H IREPO-TOKEN:${icodes[answers.type].token} http://irepo.baidu-int.com/rest/prod/v3/${module}/releases/${version}/files -o output.tar.gz
                tar -xzvf output.tar.gz
            `);
            shell.mv('output/{.,}*', './');
            shell.rm('-rf', 'output.tar.gz');
            shell.rm('-rf', 'output');
        }

        // export bce-config.js
        let config = fs.readFileSync(path.resolve(__dirname, `../init-config/${answers.type}-config.js`), 'utf8');

        if (!answers.openFlag) {
            config = config.replace('{flags}', '');
        }

        for (let key in answers) {
            let c = '';
            let val = answers[key];

            if (key === 'flags') {
                c = '\'';
                val = `['${answers.flags.toUpperCase().split(',').join("\', \'")}']`;
            }
            else if (key === 'service') {
                val = val.toUpperCase();
            }
            else if (key === 'pathname') {
                val = val.toLowerCase();
            }

            config = config.replace(new RegExp(`${c}{${key}}${c}`, 'g'), val);
        }

        fs.writeFileSync(
            path.resolve(process.cwd(), './bce-config.js'),
            config,
            {
                encoding: 'utf8'
            }
        );
    }
};
