/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file update-provider.js
 * <AUTHOR> (<EMAIL>)
 */

const chalk = require('chalk');
const childProcess = require('child_process');

const {name, version} = require('../../package.json');

module.exports = class AutoUpdater {
    static asyncCheckVersion() {
        childProcess.exec(
            `npm view ${name} version --registry=http://registry.npm.baidu-int.com`,
            (err, data) => {
                if (!err && data.trim() !== version) {
                    console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
                    console.log(chalk.greenBright(`\n\t\t\t\t\t 发现CLI新版本 ${data.trim()}`));
                    console.log(chalk.greenBright(
                        `\n\t\t npm update ${name} -g --registry=http://registry.npm.baidu-int.com`
                    ));
                    console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
                }
            }
        );
    }
};
