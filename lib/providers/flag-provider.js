/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file flag-provider.js
 * <AUTHOR> (<EMAIL>)
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const {greenBright, gray} = require('chalk');

const {resolveDefaultConfig} = require('../util');
const logger = require('../../logger');

module.exports = class FlagProvider {
    static aciton(context) {

        const kBceConfig = resolveDefaultConfig(require(path.resolve(process.cwd(), context.config)), context);
        const moduleName = kBceConfig.pathname;

        return new Promise((resolve, reject) => {
            https.get(
                `https://console-center.bj.bcebos.com/type-definition/${moduleName}/type-definition.json`,
                res => {
                    if (res.statusCode !== 200) {
                        return reject(new Error(`BOS Server Error = ${res.statusCode}`));
                    }
                    let buffer = '';
                    res.on('data', chunk => {
                        buffer += chunk;
                    });
                    res.on('end', () => {
                        try {
                            resolve(JSON.parse(buffer));
                        }
                        catch (e) {
                            reject(new Error(`Parser Data Error = ${e.message}`));
                        }
                    });
                }
            ).on('error', e => reject(new Error(`Unexcepted Error = ${e.message}`)));
        }).then(
            datasource => {
                if (context.gentypes) {
                    FlagProvider.genTypeDefinition(moduleName, datasource);
                }
                else {
                    datasource.forEach(x => {
                        console.log(`  - ${greenBright(x.featureKey)}(${greenBright(x.name)})\n      ${gray(x.desc)}`);
                    });
                }
            },
            err => console.error(err)
        );
    }

    static genTypeDefinition(name, datasource) {

        if (!fs.existsSync(path.resolve(process.cwd(), 'package.json'))) {
            logger.error(`Current working directory not found package.json, ${process.cwd()}`);
            process.exit(-1);
        }

        const dir = path.resolve(process.cwd(), 'types');

        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir);
        }

        fs.writeFileSync(path.join(dir, 'flag.d.ts'), [
            '/** type definition created by bce-cli **/\n',
            'import {FlagService} from "@baidu/bce-runtime";\n',
            `export type ${name.toUpperCase()}FlagService = FlagService<keyof {`,
            ...datasource.map(x => `\t/** ${x.name} */\n\t'${x.featureKey}': string\n`),
            '}>'
        ].join('\n'));
    }
};
