/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * @file 代理服务
 * <AUTHOR>
 */

const fs = require('fs');
const url = require('url');
const path = require('path');
const http = require('http');
const https = require('https');
const HttpProxy = require('http-proxy');
const pkg = require('../../package.json');
const Deamon = require('../deamon');

/**
 * 缓存启动的模块
 * module: {
 *     target: 'http://localhost:8181/bos/',
 *     pathname: '/bos/',
 *     pid: 用于判断是不是还在启动中，不能同时启动两个同项目模块
 * }
 */
const __modules = new Map();

class ProxyProvider {
    static add({ module, target, pathname, pid }) {
        const history = __modules.get(module);

        try {
            history && process.kill(+history.pid, 0);
            throw new Error(`Module ${module} has been started on project!`);
        } catch (e) {
            target = url.parse(target);
            target.pathname = '/';
            delete target.host;
            target = url.format(target);

            __modules.set(module, { target, pathname, pid });
        }
    }

    static remove({ module }) {
        __modules.delete(module);
        setTimeout(() => {
            // 当前无启动模块后，关闭代理
            if (__modules.size === 0) {
                process.exit(0);
            }
        }, 0);
    }

    static do(req, res, ecb) {
        const pathname = url.parse(req.headers.referer || req.url).pathname;

        for (let module of __modules.values()) {
            if (pathname === module.pathname) {
                if (!module.proxy) {
                    module.proxy = new HttpProxy({
                        secure: false
                    });
                }

                module.proxy.web(
                    req,
                    res,
                    {
                        target: module.target
                    },
                    ecb
                );
                return;
            }
        }
        ecb(res, `bad proxy ==> ${req.url}`);
    }

    static _parseBody(res) {
        return new Promise((rs, rj) => {
            const body = [];
            res.on('data', chunk => {
                body.push(chunk);
            })
                .on('end', () => {
                    rs(Buffer.concat(body).toString());
                })
                .on('error', () => {
                    rj();
                });
        });
    }

    static async _resolve(req) {
        const body = await ProxyProvider._parseBody(req);
        const data = JSON.parse(body);
        let message = null;

        if (ProxyProvider[data.action]) {
            try {
                ProxyProvider[data.action](data);
            } catch (e) {
                message = e.toString();
            }
        } else {
            message = `Action ${data.action} is not support at version ${pkg.version}, please check!`;
        }

        return {
            success: !message,
            message
        };
    }

    static async init(template) {
        const port = await Deamon.port(template);

        if (Deamon.ping(template)) {
            process.send('Succeed');
        }

        let options = {};
        let service = http;
        try {
            module.paths.unshift(path.join(process.cwd(), 'node_modules'));
            options = {
                key: fs.readFileSync(require.resolve(`@baidu/bce-template/dist/${template}/server.key`)),
                cert: fs.readFileSync(require.resolve(`@baidu/bce-template/dist/${template}/server.crt`))
            };
            service = https;
        } catch (e) {}

        const server = service.createServer(options, async (req, res) => {
            // 启动事件注册
            if (req.url.startsWith(ProxyProvider.NotifyURL)) {
                const result = await ProxyProvider._resolve(req);
                res.statusCode = result.success ? 200 : 500;
                return res.end(JSON.stringify(result));
            }

            // 普通请求代理
            ProxyProvider.do(req, res, err => {
                res.statusCode = 500;
                res.end(
                    JSON.stringify({
                        success: false,
                        message: err.message
                    })
                );
            });
        });
        server.on('error', err => {
            process.send('Proxy server crashed! Error: ' + err.message);
        });
        server.listen(port, () => {
            Deamon.keep(template, process.pid, port);
            process.send('Succeed');
        });
    }
}

ProxyProvider.NotifyURL = '/__bce-cli/proxy';

ProxyProvider.NotifyEvents = {
    START: 'add',
    STOP: 'remove'
};

module.exports = ProxyProvider;

if (require.main === module) {
    process.title = `BCE_CLI v${pkg.version}`;
    ProxyProvider.init(process.argv[2]);
}
