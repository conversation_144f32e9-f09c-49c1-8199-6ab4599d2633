/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * @file 工具集
 * <AUTHOR>
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');
const netutil = require('netutil');
const fx = require('mkdir-recursive');
const logger = require('../logger');


exports.readTmpFile = (template, filePath) => {
    const data = fs.readFileSync(
        path.resolve(os.homedir(), '.bce-cli', template, filePath),
        'utf8'
    );
    return JSON.parse(data);
};


exports.writeTmpFile = (template, filePath, data) => {
    filePath = path.resolve(os.homedir(), '.bce-cli', template, filePath);
    const dir = path.resolve(filePath, '../');
    try {
        if (!fs.statSync(dir).isDirectory()) {
            throw new Error('不是文件夹');
        }
    }
    catch (e) {
        fx.mkdirSync(dir);
    }

    return fs.writeFileSync(
        filePath,
        JSON.stringify(data),
        {
            encoding: 'utf8'
        }
    );
};

exports.findFreePort = (pivot = 8989) => {
    return new Promise((rs, rj) => {
        netutil.findFreePort(8000, 9999, pivot, '127.0.0.1', (err, port) => {
            if (err) {
                rj(err);
            }
            rs(+port);
        });
    });
};

exports.resolveDefaultConfig = (kBceConfig, context) => {
    // eslint-disable-next-line
    const pkg = require(path.resolve(process.cwd(), 'package.json'));

    if (!context.sdk) {
        kBceConfig.pathname = kBceConfig.pathname || kBceConfig.service.toLocaleLowerCase();
        kBceConfig.template = context.template;
    }
    else {
        kBceConfig.name = kBceConfig.name || pkg.name;
    }

    return kBceConfig;
};

const resolveProjectRoot = function (template) {
    try {
        // template模块读取是否存在已有的项目模板
        require.resolve(`@baidu/bce-template/dist/${template}/.config.js`);
        return `@baidu/bce-template/dist/${template}/`;
    }
    catch (e) {
    }

    try {
        // 在当前目录中读取是否存在类似项目配置可用
        require.resolve(path.resolve(process.cwd(), `${template}/.config.js`));
        return path.resolve(process.cwd(), `${template}`);
    }
    catch (e) {

    }
};

exports.resolveProjectConfig = function (mode, kBceConfig, context) {
    let projectConfig = null;
    // npm link查找模块安装包使用
    module.paths.unshift(path.join(process.cwd(), 'node_modules'));

    const template = !context.sdk && kBceConfig.template;
    if (!template) {
        return {};
    }

    const root = resolveProjectRoot(template);

    if (!root) {
        logger.error(`Can not find project template directory ${template}!`);
        process.exit(1);
    }

    // eslint-disable-next-line
    const builder = require(`${root}/.config.js`);
    projectConfig = builder(mode, kBceConfig, context);

    try {
        // eslint-disable-next-line
        projectConfig.moduleConf = require(`${root}/module-conf.json`);
    }
    catch (e) {}


    projectConfig.eslConf = JSON.parse(JSON.stringify(projectConfig.moduleConf || {}));

    // build时需要把资源不从cdn获取，改为本地相对路径
    if (mode !== 'development') {
        Object.keys(projectConfig.eslConf.paths).forEach(lib => {
            const deployPath = projectConfig.eslConf.paths[lib]
                .replace('https://code.bdstatic.com', '.')
                .replace('https://bce.bdstatic.com', '.');

            projectConfig.eslConf.paths[lib] = deployPath;
        });
    }

    return {
        ...projectConfig,
        root
    };
};

exports.http = class {
    static get(url) {
        const api = /^https/.test(url) ? https : http;

        return new Promise((resolve, reject) => {
            const req = api.get(url, res => {
                const {statusCode} = res;

                let data = '';
                res.setEncoding('utf8');
                res.on('data', chunk => data += chunk); // eslint-disable-line no-return-assign
                res.on('end', () => {
                    if (statusCode === 200) {
                        resolve(data);
                        return;
                    }
                    reject(data);
                });
            });

            req.on('error', reject);
        });
    }
};
