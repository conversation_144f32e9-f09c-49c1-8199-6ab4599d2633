/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * @file 管理守护进程
 * <AUTHOR>
 */

const path = require('path');
const { fork } = require('child_process');
const { readTmpFile, writeTmpFile, findFreePort } = require('./util');

module.exports = class Deamon {
    static start(template) {
        if (Deamon.ping(template)) {
            return Promise.resolve();
        }
        return new Promise((rs, rj) => {
            const procs = fork(path.resolve(__dirname, 'proxy-server/index.js'), [template], {
                detached: true,
                cwd: process.cwd(),
                stdio: ['inherit', 'inherit', 'inherit', 'ipc']
            });

            procs.on('message', msg => {
                if (msg === 'Succeed') {
                    rs();
                    return;
                }

                if (msg.startsWith('Failed')) {
                    rj(msg);
                    return;
                }

                console.log('Server message: ' + msg);
            });

            procs.on('exit', () => {
                rj();
            });
        });
    }

    static keep(template, pid, port) {
        writeTmpFile(template, 'pid', pid);
        writeTmpFile(template, 'port', port);
    }

    static async port(template) {
        try {
            return +readTmpFile(template, 'port');
        } catch (e) {
            const port = await findFreePort(8899);
            writeTmpFile(template, 'port', port + '');
            return port;
        }
    }

    // 判断守护进程是否已开启
    static ping(template) {
        let pid = null;

        try {
            // 进程未开启过
            pid = readTmpFile(template, 'pid');

            // 测试上一次开启的是否还开着 避免关机杀掉进程
            process.kill(+pid, 0);
        } catch (e) {
            return false;
        }

        return true;
    }
};
