/**
 * Copyright (c) 2019 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * 输出日志
 *
 * @file index.js
 * <AUTHOR> (<EMAIL>)
 */

const logger = require('debug');
const {success, warning, error} = require('log-symbols');
const {greenBright, yellowBright, redBright} = require('chalk');

logger.enable('bce-cli:*');

exports = module.exports = {
    info(formater, ...args) {
        logger(`bce-cli:info  ${success}`)(greenBright(formater), ...args);
    },
    warn(formater, ...args) {
        logger(`bce-cli:warn  ${warning}`)(yellowBright(formater), ...args);
    },
    error(formater, ...args) {
        logger(`bce-cli:error ${error}`)(redBright(formater), ...args);
    }
};
