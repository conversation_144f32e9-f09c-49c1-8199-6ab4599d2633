/**
 * 构建器
 *
 * @file builder.js
 * <AUTHOR>
 */

import fs from 'fs';
import path from 'path';
import webpack from 'webpack';
import childProcess from 'child_process';
import webpackConfig from './build-config/webpack.config.prod';

import logger from '../logger';
import collectModule from './helper/collect';
import {getBuildConfig} from './helper/project';

export default class DevServer {
    static startup(options = {}) {
        const mode = 'production';
        const {dir, i18n, module} = options;

        if (!fs.statSync(dir).isDirectory()) {
            logger.error(`Current working directory invalid, ${dir}`);
            process.exit(-1);
        }

        if (!fs.existsSync(path.resolve(dir, 'package.json'))) {
            logger.error(`Current working directory not found package.json, ${dir}`);
            process.exit(-1);
        }

        const kMapping = collectModule(mode, options);

        const buildConfig = getBuildConfig(mode, options, kMapping);

        const config = webpackConfig({
            i18n,
            kMapping,
            ...buildConfig,
            template: options.template
        });

        webpack(config, (err, stats) => {
            process.stdout.write(`${stats.toString({colors: true})}\n`);

            if (err || stats.hasErrors()) {
                logger.error('Build failed!! ');
            } else {
                logger.info('Build succeed~');

                // 是否进行国际化，上传语料
                if (i18n && module) {
                    const m = module.split(',')[0]; // 取首个模块最为国际化上传模块
                    if (!fs.existsSync(path.resolve(dir, 'dist/i18n'))) {
                        logger.error(`I18n terms directory %o is not found, ${dir}`);
                        process.exit(-1);
                    }

                    logger.info('Start to upload i18n terms');
                    childProcess.execSync(
                        `npx bce-i18n upload -d dist/i18n --module ${m} && rm -rf dist/i18n`,
                        {
                            env: {
                                ...process.env,
                                NODE_ENV: 'production'
                            },
                            cwd: dir,
                            stdio: [0, 1, 2]
                        }
                    );
                }

                // TODO dependecies内容拷贝到build目录
            }
        });
    }
}
