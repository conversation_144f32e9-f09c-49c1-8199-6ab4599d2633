/**
 * Dev server
 *
 * @file dev-server.js
 * <AUTHOR>
 */

import fs from 'fs';
import {blueBright} from 'chalk';
import webpack from 'webpack';
import WebpackDevServer from 'webpack-dev-server';
import webpackConfig from './build-config/webpack.config.dev';

import logger from '../logger';
import collectModule from './helper/collect';
import {getBuildConfig} from './helper/project';

export default class DevServer {
    static boot(options = {}) {
        const config = webpackConfig(options);
        const compiler = webpack(config);
        const server = new WebpackDevServer(compiler, config.devServer);

        server.listen(config.devServer.port, config.devServer.host, () => {
            logger.info(`Available on: ${blueBright(options.proxy.publicPath)}`);
            logger.info(`Proxy to: ${blueBright(options.proxyPath)}`);

            if (Object.keys(options.kMapping.module).length) {
                logger.info(`Packing module: ${blueBright(Object.keys(options.kMapping.module))}`);
            }
            if (Object.keys(options.kMapping.sdk).length) {
                logger.info(`Packing sdk: ${blueBright(Object.keys(options.kMapping.sdk))}`);
            }
        });
    }

    static startup(options) {
        const mode = 'development';
        const {dir, i18n} = options;

        if (!fs.statSync(dir).isDirectory()) {
            logger.error(`Current working directory invalid, ${dir}`);
            process.exit(-1);
        }

        const kMapping = collectModule(mode, options);

        if (!Object.keys(kMapping.module).length && !Object.keys(kMapping.sdk).length) {
            logger.error(`Current working directory not found console project! ${options.dir}`);
            process.exit(-1);
        }

        const config = getBuildConfig(mode, options, kMapping);

        DevServer.boot({i18n, kMapping, ...config, template: options.template});
    }
}
