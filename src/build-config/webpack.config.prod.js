/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.config.prod.js
 * <AUTHOR>
 */

const path = require('path');
const webpack = require('webpack');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const UglifyJSPlugin = require('uglifyjs-webpack-plugin');

const baseConfig = require('./webpack.config.base');

exports = module.exports = options => {
    const {kMapping, htmlParam} = options;
    const modules = [...Object.values(kMapping.module), ...Object.values(kMapping.sdk)];

    const entry = modules
        .reduce((ctx, {filepath, library}) => {
            ctx[library] = filepath;
            return ctx;
        }, {});

    const kPlugins = [];
    const output = {};
    if (Object.values(kMapping.module).length) {
        kPlugins.push(new HtmlWebpackPlugin({
            filename: 'index.html',
            template: options.index,
            inject: 'body',
            ...htmlParam
        }));
        output.library = '[name]';
    }

    return merge.smart(baseConfig(options), {
        mode: 'production',

        devtool: 'source-map',

        entry,

        output: {
            ...output,
            path: path.join(process.cwd(), 'dist')
        },

        plugins: [
            new webpack.NoEmitOnErrorsPlugin(),

            new UglifyJSPlugin({
                parallel: true,
                sourceMap: true
            }),

            ...kPlugins
        ]
    });
};
