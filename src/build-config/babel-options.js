/**
 * babel配置
 *
 * @file babel-options.js
 * <AUTHOR>
 */


exports = module.exports = ({i18n, kMapping}) => {

    const isSDK = !!Object.values(kMapping.sdk).length;

    const babelOptions = {
        cacheDirectory: process.env.NODE_ENV !== 'production',
        presets: [
            [
                require('@babel/preset-env'),
                isSDK
                    ? {
                        useBuiltIns: 'usage',
                        corejs: 3,
                        targets: 'defaults, ie >= 10'
                    }
                    : {
                        targets: '> 0.5%, last 2 versions, Firefox ESR, not dead'
                    } // https://browserl.ist/
            ],
            require('@babel/preset-typescript')
        ],
        plugins: [
            require('@babel/plugin-syntax-dynamic-import'),
            [require('@babel/plugin-proposal-decorators'), {legacy: true}],
            [require('@babel/plugin-proposal-class-properties'), {loose: true}]
        ]
    };

    if (i18n) {
        babelOptions.plugins.push(
            [require('@baiducloud/i18n/transform-plugin'), {
                library: '@baiducloud/i18n',
                output: 'dist/i18n'
            }]
        );
    }
    return babelOptions;
};
