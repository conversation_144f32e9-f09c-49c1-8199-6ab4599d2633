/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.config.dev.js
 * <AUTHOR>
 */

const webpack = require('webpack');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const baseConfig = require('./webpack.config.base');

exports = module.exports = options => {
    const {kMapping, proxy, htmlParam} = options;
    const entry = [...Object.values(kMapping.module), ...Object.values(kMapping.sdk)]
        .reduce((ctx, {filepath, library}) => {
            ctx[library] = [
                `webpack-dev-server/client?${proxy.publicPath}`,
                'webpack/hot/only-dev-server',
                filepath
            ];
            return ctx;
        }, {});


    return merge.smart(baseConfig(options), {
        mode: 'development',

        entry,

        output: {
            library: '[name]',
            publicPath: proxy.publicPath
        },

        plugins: [
            new HtmlWebpackPlugin({
                filename: 'index.html',
                template: options.index,
                inject: 'body',
                ...htmlParam
            }),

            new webpack.HotModuleReplacementPlugin(),

            new webpack.LoaderOptionsPlugin({
                debug: true
            })
        ],

        devServer: merge.smart({
            host: '0.0.0.0',
            compress: true,
            noInfo: true,
            stats: 'errors-only',
            inline: true,
            lazy: false,
            hot: true,
            disableHostCheck: true,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Credentials': true
            },
            watchOptions: {
                aggregateTimeout: 300,
                ignored: /node_modules/,
                poll: 100
            },
            historyApiFallback: {
                verbose: true,
                disableDotRule: false
            }
        }, proxy)
    });
};
