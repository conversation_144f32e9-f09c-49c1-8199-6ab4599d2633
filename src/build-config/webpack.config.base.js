/**
 * Base webpack config used across other specific configs
 *
 * @file webpack.base.config.js
 * <AUTHOR>
 */

/* eslint import/no-dynamic-require:off */

const path = require('path');
const webpack = require('webpack');
const cssnano = require('cssnano');
const autoprefixer = require('autoprefixer');
const LessFunc = require('less-plugin-functions');

const babelOptions = require('./babel-options');

exports = module.exports = (options) => {
    const {kMapping, externals, plugins} = options;
    const modules = [...Object.values(kMapping.module), ...Object.values(kMapping.sdk)];

    return {
        mode: 'none',

        context: path.resolve(__dirname, '../'),

        resolve: {
            extensions: ['.js', '.ts', '.json']
        },

        module: {
            rules: [
                {
                    test: /\.(ts|js)$/,
                    exclude: /node_modules/,
                    use: {
                        loader: 'babel-loader',
                        options: babelOptions(options)
                    }
                },
                {
                    test: /\.(css|less)$/,
                    use: [
                        {loader: 'style-loader'},
                        {loader: 'css-loader'},
                        {
                            loader: 'postcss-loader',
                            options: {
                                plugins: [
                                    cssnano({preset: 'default'}),
                                    autoprefixer()
                                ]
                            }
                        },
                        {
                            loader: 'less-loader',
                            options: {
                                plugins: [new LessFunc()]
                            }
                        }
                    ]
                },
                // SVG Font
                {
                    test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
                    use: {
                        loader: 'url-loader',
                        options: {
                            limit: 10000,
                            mimetype: 'image/svg+xml'
                        }
                    }
                },
                // Common Image Formats
                {
                    test: /\.(?:ico|gif|png|jpg|jpeg|webp)$/,
                    use: 'url-loader'
                }
            ]
        },

        output: {
            libraryTarget: 'amd',
            // umdNamedDefine: true,
            path: path.join(process.cwd(), 'dist'),
            filename: ({chunk: {name}}) => `${modules.find(({library}) => name === library).filename}.js`
        },

        plugins: [
            new webpack.EnvironmentPlugin({
                VERSION: JSON.stringify(process.env.VERSION || 'DEV'),
                NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'production')
            }),

            new webpack.NamedModulesPlugin(),

            ...(plugins || [])
        ],

        externals
    };
};
