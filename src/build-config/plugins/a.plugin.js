/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file a.pugin.js
 * <AUTHOR> (<EMAIL>)
 */

const {ConcatSource} = require('webpack-sources');
const ExternalModule = require('webpack/lib/ExternalModule');
const CommonJsRequireDependency = require('webpack/lib/dependencies/CommonJsRequireDependency');

const http = require('./http');

module.exports = class {
    constructor(options) {
        this.templateId = options.templateId;
        this.template = options.template;
        this.modules = options.modules;
        this.exportName = `@builtin/${this.modules[0]}/flags`;

        if (!this.templateId || !this.modules.length) {
            throw new Error(`Error: Plugin init error, templateId = ${this.templateId} modules = ${this.modules}`);
        }
    }

    renderSource(isDevelopment) {
        const flagSourceURLs = this.modules.map(moduleName =>
            'https://console-center.bj.bcebos.com/type-definition/'
                + `${moduleName.toLowerCase()}/type-definition@${this.templateId}`);

        return new Promise((resolve, reject) => {
            if (isDevelopment) {
                const f = flagSourceURLs.map((u, i) => `f${i + 1}`);
                const a = `(${f.join(').concat(')})`;

                return resolve(`
                    (function(global){
                        global.__TEMPLATE = '${this.templateId}';
                        define(
                            '${this.exportName}',
                            ['@baiducloud/runtime', '${flagSourceURLs.join('\',\'')}'],
                            function(r,${f.join(',')}){
                                with(r.ServiceFactory){register('$flag', create('$flag', ${a}, '${this.template}'))}
                            }
                        );
                    })(window);
                `.replace(/\s/g, ''));
            }

            const reqs = flagSourceURLs.map(item => http.get(`${item}.json`));

            Promise.all(reqs).then(res => {
                const f = res.reduce((x, y) => {
                    const o = JSON.parse(y);
                    return o.code && o.code === 'NoSuchKey' ? x : x.concat(o); // 过滤加载失败的flag
                }, []);

                const a = `${JSON.stringify(f)}, '${this.template}'`;

                return resolve(`
                    (function(global){
                        global.__TEMPLATE = '${this.templateId}';
                        define('${this.exportName}',['@baiducloud/runtime'],function(r){
                            with(r.ServiceFactory){register('$flag', create('$flag', ${a}))}
                        });
                    })(window);
                `.replace(/\s/g, ''));
            }, reject);
        });
    }

    apply(compiler) {
        compiler.hooks.compilation.tap('APlugin', (compilation) => {
            compilation.hooks.succeedModule.tap('APlugin', m => {
                m.dependencies.push(new CommonJsRequireDependency(this.exportName, null));
            });

            compilation.hooks.optimizeChunkAssets.tapAsync('APlugin', (chunks, callback) => {
                const sources = [];
                for (const chunk of chunks) {           // eslint-disable-line no-restricted-syntax
                    if (!chunk.canBeInitial()) {
                        continue;                       // eslint-disable-line no-continue
                    }

                    for (const file of chunk.files) {   // eslint-disable-line no-restricted-syntax
                        if (/bootstrap\.js$/.test(file)) {
                            sources.push(file);
                        }
                    }
                }

                this.renderSource(compiler.options.mode === 'development').then(
                    res => {
                        sources.forEach(
                            file => compilation.updateAsset(file, old => new ConcatSource(res, '\n', old))
                        );
                        callback();
                    },
                    callback
                );
            });
        });

        compiler.hooks.normalModuleFactory.tap('APlugin', nmf => {
            nmf.hooks.factory.tap('APlugin', factory => (data, callback) => {
                return data.request === this.exportName
                    ? callback(null, new ExternalModule(this.exportName, 'amd', this.exportName))
                    : factory(data, callback);
            });
        });
    }
};
