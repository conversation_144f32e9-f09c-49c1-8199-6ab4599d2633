/**
 * @file 整理项目配置
 * <AUTHOR>
 */
import path from 'path';
import fs from 'fs';
import webpack from 'webpack';
import merge from 'webpack-merge';

import {getMockConfig} from './mock';
import sdkLoaderBanner from './banner';
import APlugin from '../build-config/plugins/a.plugin';
import PackageManager from '../package-manager';


// 获取moduleConf的内容
export const getModuleConf = template => {

    try {
        // eslint-disable-next-line import/no-dynamic-require
        return require(`@baiducloud/template/dist/${template}/module-conf.json`);
    } catch (ex) {
        return {};
    }
};

// 组织external
export const parseWebpackExternals = rules => {
    return [
        // eslint-disable-next-line arrow-body-style
        (context, request, callback) => {

            const module = rules.find(({dir}) => context.startsWith(dir));
            if (!module) {
                return callback();
            }

            // eslint-disable-next-line no-restricted-syntax
            for (const ext of module.externals) {

                // cdn资源按照安装的版本加载
                if (ext instanceof Array && ext[0] === request) {
                    return callback(null, `umd ${ext[0]}`);
                }

                if (ext instanceof Function && ext(context, request, callback)) {
                    return;
                }

                if (ext === request) {
                    return callback(null, `umd ${request}`);
                }
            }

            return callback();
        }
    ];
};


// 转换dependencies为esl配置
const parseRequireBanner = (dir, dependencies, from) => {
    const pathConfig = Object.entries(dependencies)
        .map(PackageManager.normalizeDeps)
        .map(({libName, libPath, libVersion}) => {
            // 暂时注掉，私有环境后续改一下直接从远程拉
            // if (from === 'local') {
            //     // 从本地node里读取
            //     libPath = `/${path.relative(
            //         process.cwd(),
            //         require.resolve(path.resolve(dir, 'node_modules', libName))
            //     )}`;
            // }

            return [`${libName}@${libVersion}`, libPath];
        })
        .reduce((ctx, [key, value]) => {
            ctx[key] = value.replace(/\.js$/, '');
            return ctx;
        }, {});

    return `(function () {
        require.config({paths: ${JSON.stringify(pathConfig)}});
    })();`;
};

// eslint-disable-next-line import/no-dynamic-require
export const getProjectConfig = options => require(`@baiducloud/template/dist/${options.template}/.config.js`);


// 组织本次启动的项目配置
export const getBuildConfig = (mode, options, kMapping) => {

    // eslint-disable-next-line import/no-dynamic-require
    const projectConfig = getProjectConfig(options)(mode, options, kMapping);
    const config = {
        plugins: projectConfig.plugins || []
    };

    // index模板，优先使用用户自定义html，自定义模板不能同时父目录启动
    let index = path.resolve(process.cwd(), 'index.html');
    const isCustomIndexPage = fs.existsSync(index);

    if (!isCustomIndexPage) {
        index = require.resolve(`@baiducloud/template/dist/${options.template}/index.html`);
    }

    config.index = index;
    config.htmlParam = projectConfig.html || {};

    // 排除包整理，包含：pkg(build.dependencies) & module-conf & .config.js -> externals
    const moduleConf = getModuleConf(options.template);
    const projectExternals = projectConfig.externals || [];
    const externalRules = [
        ...Object.values(kMapping.module),
        ...Object.values(kMapping.sdk)
    ].map(({dir, dependencies, externals, filename}) => {
        // 如果模块本身配置了externals的话 则只排除其声明的排除库
        if (!externals) {
            externals = [];

            // build.dependencies也排除在外，增加文件banner，esl require配置按需加载
            if (dependencies) {
                externals = [...externals, (context, request, callback) => {
                    // 处理包 + 子包
                    if (dependencies) {
                        // @baidu/sui @baidu/sui-biz 不同的install
                        // @baidu/bce-xx-sdk @baidu/bce-xx-sdk/san 同一个install
                        const installedDep = Object.keys(dependencies).find(
                            d => request === d || request.startsWith(`${d}/`)
                        );

                        if (installedDep) {
                            // eslint-disable-next-line max-len
                            const {libName, libVersion} = PackageManager.normalizeDeps([installedDep, dependencies[installedDep]]);
                            callback(null, `umd ${libName}@${libVersion}${request.replace(installedDep, '')}`);
                            return true;
                        }

                        return false;
                    }
                }];

                const libRequire = parseRequireBanner(dir, dependencies, projectConfig.deps.from);
                config.plugins.push(
                    new webpack.BannerPlugin({
                        test: new RegExp(filename),
                        banner: libRequire || '',
                        raw: true
                    })
                );
            }

            // 兼容未使用安装方式的模块
            externals = [
                'framework',
                ...externals,
                ...projectExternals,
                ...Object.keys(moduleConf.paths),
                ...Object.values(moduleConf.bundles).reduce((ctx, val) => ctx.concat(val), []),
                (context, request, callback) => {
                    if (request.startsWith('@baiducloud/') || request.startsWith('@baidu/')) {
                        callback(null, `umd ${request}`);
                        return true;
                    }
                }
            ];
        }

        return {dir, externals};
    });
    config.externals = parseWebpackExternals(externalRules);

    if (options.flag && Object.keys(kMapping.module).length) {
        const moduleName = Object.keys(kMapping.module)[0];
        config.plugins.push(new APlugin({
            modules: options.module ? options.module.split(',') : (moduleName ? [moduleName] : []),
            templateId: projectConfig.templateId,
            template: options.template
        }));
    }


    // 下面都是dev环境下需要的参数组织逻辑
    if (mode !== 'development') {
        return config;
    }

    // 启动与代理地址
    const {proxy = projectConfig.proxy.site, port = '8889'} = options;
    const url = new URL(proxy);

    config.proxy = projectConfig.proxy.more || {};
    config.proxyPath = url.toString();

    url.port = port;
    url.host = `localhost.${url.host}`;

    config.proxy.publicPath = url.toString();
    config.proxy.port = port;
    config.proxy.proxy = projectConfig.proxy.rules(proxy);

    // mockup
    const modules = [...Object.values(kMapping.module)];
    // 暂时只做单模块启动下的mock，sdk和多模块启动先不支持
    if (options.mock && modules.length === 1) {
        const mockConfig = getMockConfig(modules[0].mockrc, options.template);
        Object.keys(config.proxy.proxy).forEach(key => {
            config.proxy.proxy[key] = merge.smart(config.proxy.proxy[key], mockConfig);
        });
    }

    // 父目录启动时，优先进行本地启动的工具项目进行联调，便于sdk的开发
    const banner = sdkLoaderBanner(config.proxy.publicPath, kMapping, options);
    if (banner) {
        config.plugins.push(
            new webpack.BannerPlugin({
                test: /\.js/,
                banner,
                raw: true
            })
        );
    }

    return config;
};
