/**
 * @file 项目相关工具
 * <AUTHOR>
 */
import path from 'path';
import fs from 'fs';

import {DEFAULT_MOCKRC} from './mock';
import logger from '../../logger';
import {name as cliName} from '../../package.json';


// 收集模块配置
const collectModuleConfig = (mode, kMapping, dir) => {
    let pkg = null;
    try {
        pkg = fs.readFileSync(path.resolve(dir, 'package.json'));
    } catch (ex) {
        return;
    }

    pkg = JSON.parse(pkg);

    const {name, dependencies = {}, devDependencies = {}, build = {}} = pkg;
    const {entry, moduleName, externals} = build;

    // get mockrc
    let mockrc = null;
    const rcpath = path.resolve(dir, '.mockrc');
    if (fs.existsSync(rcpath)) {
        try {
            mockrc = JSON.parse(fs.readFileSync(rcpath).toString());
        } catch (ex) {
            throw new Error(`解析.mockrc文件出错，${ex.toString()}`);
        }
    }
    mockrc = Object.assign(DEFAULT_MOCKRC, mockrc || build.mockrc || {});

    // 不使用cli的项目直接排除
    if (!dependencies[cliName] && !devDependencies[cliName]) {
        return;
    }

    const buildDeps = build.dependencies || {};

    // 工具库
    if (/^@baiducloud\//.test(name) || /^@baidu\//.test(name)) {
        const libraryScope = /^@baiducloud\//.test(name) ? '@baiducloud' : '@baidu';

        // 工具库支持配置多个entry，区分界面工具、纯SDK工具等
        if (entry) {
            // sdk可能存在多个输出文件
            Object.keys(entry).forEach(kOutputName => {
                kMapping.sdk[kOutputName] = {
                    filepath: path.resolve(dir, entry[kOutputName]),
                    library: `${libraryScope}/${kOutputName}`,
                    filename: kOutputName,
                    dependencies: buildDeps,
                    externals,
                    mockrc,
                    dir
                };
            });

        } else {
            // 直接使用默认的文件，如果没有webpack运行会自动报错
            // 截取`@baidulcoud/`后的内容为输出文件名称
            const kSDKName = name.replace('@baidulcoud/', '');
            kMapping.sdk[kSDKName] = {
                filepath: path.resolve(dir, 'src/sdk/index.ts'),
                library: `${libraryScope}/${kSDKName}`,
                filename: kSDKName,
                dependencies: buildDeps,
                externals,
                mockrc,
                dir
            };
        }
    } else {
        const kAppName = moduleName || name.replace('console-', '');
        const library = `${kAppName}/bootstrap`;

        kMapping.module[kAppName] = {
            library,
            filepath: require.resolve(dir),
            filename: mode === 'development' ? library : 'bootstrap',
            dependencies: buildDeps,
            externals,
            mockrc,
            dir
        };
    }
};


export default (mode, {dir}) => {
    if (!fs.statSync(dir).isDirectory()) {
        logger.error(`Current working directory invalid, ${dir}`);
        process.exit(-1);
    }

    const kMapping = {module: {}, sdk: {}};

    try {
        // 启动单个项目
        if (collectModuleConfig(mode, kMapping, dir)) {
            return kMapping;
        }
    } catch (ex) {
        logger.error(`Collect module error in dir ===> ${dir}, details: ${ex.toString()}`);
    }

    // 启动多个项目及SDK等
    fs.readdirSync('.', {withFileTypes: true})
        .forEach(fd => {
            const dir2 = path.resolve('.', fd.name || fd);
            try {
                collectModuleConfig(mode, kMapping, dir2);
            } catch (ex) {
                logger.error(`Collect module error in dir ===> ${dir2}, details: ${ex.toString()}`);
            }
        });

    return kMapping;
};
