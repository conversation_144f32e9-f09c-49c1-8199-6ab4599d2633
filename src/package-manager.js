/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file list-deps.js
 * <AUTHOR> (<EMAIL>)
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import https from 'https';
import childProcess from 'child_process';

export const MasterLibReg = /^((@baiducloud\/)|(@baidu\/bce-)|(@baidu\/sui))/;

export default class PackageManager {

    static normalizeDeps([keyName, pathName]) {
        const libName = keyName.replace(/^@npm\//g, '');
        let libPath = null;
        let libVersion = null;

        if (keyName.startsWith('@npm/')) {
            // "@npm/humanize": "humanize@0.0.9/humanize.js"
            libVersion = pathName.replace(`${libName}@`, '');
            libVersion = libVersion.substring(0, libVersion.indexOf('/'));
            libPath = `https://code.bdstatic.com/npm/${pathName}`;
        }
        else {
            // "@baiducloud/tag-sdk": "@baiducloud/fe-tag-sdk/1.0.15.2/tag-sdk.js"
            libVersion = pathName.match(/([\d]+\.){3}[\d]+/g);
            libPath = `https://bce.bdstatic.com/lib/${pathName}`;
        }
        return {libName, libPath, libVersion};
    }


    static list() {
        // eslint-disable-next-line
        const {build = {}} = require(path.join(process.cwd(), 'package.json'));

        // eslint-disable-next-line arrow-body-style
        const normalizeDeps = ([keyName, pathName]) => {
            return MasterLibReg.test(keyName)
                ? [`${keyName}@${pathName.match(/(\d+\.){3}\d+/g)}`, `https://bce.bdstatic.com/lib/${pathName}`]
                : [pathName.split('/')[0], `https://code.bdstatic.com/npm/${pathName}`];
        };

        console.log(chalk.gray(`\n\t${'-'.repeat(88)}\n`));
        Object.entries(build.dependencies || {}).forEach(item => {
            const [key] = normalizeDeps(item);
            const version = key.split('@').slice(-1);
            const prefix = key.split('@').slice(0, -1).join('@');

            console.log(chalk.greenBright(`\t ${prefix}@${version}`) + chalk.gray(` ==> ${item[1]}`));
        });
        console.log(chalk.redBright('\n\t ⚠️  Npm资源路径取决于`package.main`配置，自行检查资源是`minify`版本！'));
        console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
    }

    static install(...names) {
        const promises = names.map(installName => {
            if (MasterLibReg.test(installName)) {
                return new Promise((resolve, reject) => {
                    https.get('https://bce.bdstatic.com/console/static/online-config.js', res => {
                        if (res.statusCode !== 200) {
                            return reject(installName);
                        }

                        let buffer = '';
                        res.on('data', chunk => {
                            buffer += chunk;
                        });
                        res.on('end', () => {
                            try {
                                const onlineConf = JSON.parse(buffer.slice(24, -4));
                                const modulePath = onlineConf[installName].path
                                    .replace('https://bce.bdstatic.com/lib/', '');

                                resolve({[installName]: `${modulePath}.js`});
                            } catch (e) {
                                reject(installName);
                            }
                        });
                    }).on('error', () => reject(installName));
                });
            }

            return new Promise(
                (resolve, reject) => childProcess.exec(
                    `npm view ${installName} main name version browser -json --registry=http://registry.npm.baidu-int.com`,
                    (err, data) => {
                        const {main, name, version, browser} = JSON.parse(data);

                        return err ? reject(name) : resolve({
                            [`@npm/${name}`]: `${name}@${version}/${browser || main}`
                        });
                    }
                )
            );
        });

        Promise.all(promises).then(
            deps => deps.reduce((ctx, item) => Object.assign(ctx, item), {})
        ).then(
            res => {
                const pkgPath = path.join(process.cwd(), 'package.json');
                const pkg = JSON.parse(fs.readFileSync(pkgPath));

                pkg.build = Object.assign({}, pkg.build, {
                    dependencies: Object.assign({}, pkg.build && pkg.build.dependencies, res)
                });

                fs.writeFileSync(pkgPath, JSON.stringify(pkg, 1, '\t'));

                PackageManager.list();
            },
            name => {
                console.log(chalk.gray(`\n\t${'-'.repeat(88)}\n`));
                console.log(chalk.redBright(`\t install ${name} failed!`));
                console.log(chalk.gray(`\n\t${'-'.repeat(88)}`));
            }
        );
    }
}
