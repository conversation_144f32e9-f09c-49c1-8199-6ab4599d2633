#!/bin/sh
# export PATH=$NODEJS_12_16_1_BIN:$YARN_1_22_4_BIN:$PATH

# # 方案 V1.0.0

# rm -rf npm
# mkdir npm

# cp -rf lib npm/
# cp -rf logger npm/
# cp index.js npm/
# cp package.json npm/
# cp README.md npm/
# cp .npmignore npm/
# cp .npmrc npm/

# cd npm

# VERSION=$(node -e "(function() { console.log(require('./package.json').version) })()")
# TYPE=$(echo ${VERSION} | tr -cd "^[A-Za-z]+$")
# [ -n "$TYPE" ] && npm publish --tag ${TYPE} || npm publish

# cd ..

# mv npm output


# 新方案 V1.1.0 简化.sh

# rm -rf output

# if [ ! -d "output" ]; then
# mkdir output
# fi

# cp -rf `ls|grep -v output|xargs` output

# cd output
echo "node: $(node -v)"
echo "npm: v$(npm -v)"

VERSION=$(node -e "(function() { console.log(require('./package.json').version) })()")
TYPE=$(echo ${VERSION} | tr -cd "^[A-Za-z]+$")
[ -n "$TYPE" ] && npm publish --tag ${TYPE} || npm publish

mkdir output
